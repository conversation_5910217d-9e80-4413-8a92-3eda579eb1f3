"""
Flask Backend for AI Train Traffic Control System
Real-time API and WebSocket communication
"""

from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from flask_socketio import SocketIO, emit, join_room, leave_room
import json
import threading
import time
from datetime import datetime
from typing import Dict, List, Any

# Import our models
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.railway_network import RailwayNetwork, create_sample_network, Station, Track, TrainPriority
from models.ai_optimizer import HybridOptimizer, Train, TrafficState, TrainStatus
from simulation.train_simulator import TrainSimulator, SimulationConfig, SimulationMode

app = Flask(__name__,
            template_folder='../templates',
            static_folder='../static')
app.config['SECRET_KEY'] = 'railway_ai_secret_key_2025'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# Global state
network = None
simulator = None
optimizer = None
active_sessions = {}


def initialize_system():
    """Initialize the railway system"""
    global network, simulator, optimizer
    
    # Create sample network
    network = create_sample_network()
    
    # Initialize simulator
    config = SimulationConfig(
        mode=SimulationMode.REAL_TIME,
        speed_multiplier=1.0,
        auto_optimize=True,
        record_metrics=True
    )
    simulator = TrainSimulator(network, config)
    
    # Initialize optimizer
    optimizer = HybridOptimizer(network)
    
    # Register event handlers
    simulator.register_event_handler('simulation_updated', broadcast_simulation_update)
    simulator.register_event_handler('train_moved', broadcast_train_event)
    simulator.register_event_handler('train_delayed', broadcast_train_event)
    simulator.register_event_handler('optimization_completed', broadcast_optimization_result)
    
    print("Railway AI system initialized successfully")


def broadcast_simulation_update(event):
    """Broadcast simulation updates to all connected clients"""
    socketio.emit('simulation_update', event['data'], namespace='/simulation')


def broadcast_train_event(event):
    """Broadcast train events to all connected clients"""
    socketio.emit('train_event', event, namespace='/simulation')


def broadcast_optimization_result(event):
    """Broadcast optimization results to all connected clients"""
    socketio.emit('optimization_result', event['data'], namespace='/simulation')


# REST API Routes

@app.route('/')
def index():
    """Serve the main dashboard"""
    return render_template('index.html')


@app.route('/api/network/info')
def get_network_info():
    """Get railway network information"""
    if not network:
        return jsonify({'error': 'Network not initialized'}), 500
    
    return jsonify({
        'stations': [station.to_dict() for station in network.stations.values()],
        'tracks': [track.to_dict() for track in network.tracks.values()],
        'stats': network.get_network_stats()
    })


@app.route('/api/trains', methods=['GET'])
def get_trains():
    """Get all trains in the system"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    trains_data = {}
    for train_id, train in simulator.traffic_state.trains.items():
        trains_data[train_id] = simulator._serialize_train(train)
    
    return jsonify(trains_data)


@app.route('/api/trains', methods=['POST'])
def add_train():
    """Add a new train to the system"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        data = request.get_json()
        
        train = Train(
            id=data['id'],
            route=data['route'],
            priority=TrainPriority(data['priority']),
            scheduled_times=data['scheduled_times'],
            current_position=data.get('current_position', 0),
            current_time=data.get('current_time', 0.0),
            delay=data.get('delay', 0.0),
            status=TrainStatus(data.get('status', 'scheduled'))
        )
        
        simulator.add_train(train)
        
        return jsonify({
            'success': True,
            'message': f'Train {train.id} added successfully',
            'train': simulator._serialize_train(train)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 400


@app.route('/api/simulation/start', methods=['POST'])
def start_simulation():
    """Start the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.start_simulation()
        return jsonify({'success': True, 'message': 'Simulation started'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/pause', methods=['POST'])
def pause_simulation():
    """Pause/unpause the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.pause_simulation()
        status = 'paused' if simulator.is_paused else 'running'
        return jsonify({'success': True, 'status': status})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/stop', methods=['POST'])
def stop_simulation():
    """Stop the simulation"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.stop_simulation()
        return jsonify({'success': True, 'message': 'Simulation stopped'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/step', methods=['POST'])
def step_simulation():
    """Step the simulation forward"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    try:
        simulator.step_simulation()
        return jsonify({'success': True, 'time': simulator.current_time})
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/simulation/config', methods=['GET', 'POST'])
def simulation_config():
    """Get or update simulation configuration"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    if request.method == 'GET':
        return jsonify({
            'mode': simulator.config.mode.value,
            'speed_multiplier': simulator.config.speed_multiplier,
            'auto_optimize': simulator.config.auto_optimize,
            'show_delays': simulator.config.show_delays,
            'show_capacity': simulator.config.show_capacity,
            'record_metrics': simulator.config.record_metrics
        })
    
    elif request.method == 'POST':
        try:
            data = request.get_json()
            
            if 'mode' in data:
                simulator.config.mode = SimulationMode(data['mode'])
            if 'speed_multiplier' in data:
                simulator.config.speed_multiplier = float(data['speed_multiplier'])
            if 'auto_optimize' in data:
                simulator.config.auto_optimize = bool(data['auto_optimize'])
            if 'show_delays' in data:
                simulator.config.show_delays = bool(data['show_delays'])
            if 'show_capacity' in data:
                simulator.config.show_capacity = bool(data['show_capacity'])
            if 'record_metrics' in data:
                simulator.config.record_metrics = bool(data['record_metrics'])
            
            return jsonify({'success': True, 'message': 'Configuration updated'})
            
        except Exception as e:
            return jsonify({'error': str(e)}), 400


@app.route('/api/optimization/run', methods=['POST'])
def run_optimization():
    """Manually trigger optimization"""
    if not simulator or not optimizer:
        return jsonify({'error': 'System not initialized'}), 500

    try:
        result = optimizer.optimize_traffic(simulator.traffic_state)
        return jsonify({
            'success': True,
            'result': result,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/tracks/block', methods=['POST'])
def block_track():
    """Block a specific track segment"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500

    try:
        data = request.get_json()
        track_id = data.get('track_id')
        reason = data.get('reason', 'Manual disruption')

        if not track_id:
            return jsonify({'error': 'track_id is required'}), 400

        # Block the track
        simulator.traffic_state.block_track(track_id, reason)

        # Find affected trains
        affected_trains = []
        for train_id, train in simulator.traffic_state.trains.items():
            if train.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]:
                # Check if train is on this track or approaching it
                if train.current_position < len(train.route) - 1:
                    current_station = train.route[train.current_position]
                    next_station = train.route[train.current_position + 1]
                    train_track_id = f"{current_station}-{next_station}"
                    reverse_track_id = f"{next_station}-{current_station}"

                    if track_id in [train_track_id, reverse_track_id]:
                        affected_trains.append(train_id)
                        train.status = TrainStatus.DELAYED
                        train.delay += 10.0  # Add delay for disruption

        # Trigger optimization if trains are affected
        optimization_result = None
        if affected_trains and optimizer:
            optimization_result = optimizer.optimize_traffic(simulator.traffic_state)

        # Broadcast track blocked event
        socketio.emit('track_blocked', {
            'track_id': track_id,
            'reason': reason,
            'affected_trains': affected_trains,
            'optimization_triggered': optimization_result is not None,
            'timestamp': datetime.now().isoformat()
        }, namespace='/simulation')

        return jsonify({
            'success': True,
            'track_id': track_id,
            'affected_trains': affected_trains,
            'optimization_triggered': optimization_result is not None,
            'message': f'Track {track_id} blocked. {len(affected_trains)} trains affected.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/tracks/unblock', methods=['POST'])
def unblock_track():
    """Unblock a specific track segment"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500

    try:
        data = request.get_json()
        track_id = data.get('track_id')

        if not track_id:
            return jsonify({'error': 'track_id is required'}), 400

        # Unblock the track
        simulator.traffic_state.unblock_track(track_id)

        # Broadcast track unblocked event
        socketio.emit('track_unblocked', {
            'track_id': track_id,
            'timestamp': datetime.now().isoformat()
        }, namespace='/simulation')

        return jsonify({
            'success': True,
            'track_id': track_id,
            'message': f'Track {track_id} unblocked.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/tracks/status', methods=['GET'])
def get_track_status():
    """Get status of all tracks"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500

    try:
        track_status = {}

        # Get all tracks from network
        for track_id, track in network.tracks.items():
            status = 'available'
            occupying_train = None

            # Check if blocked
            if track_id in simulator.traffic_state.blocked_tracks:
                status = 'blocked'
            # Check if occupied
            elif track_id in simulator.traffic_state.track_occupancy and simulator.traffic_state.track_occupancy[track_id]:
                status = 'occupied'
                occupying_train = simulator.traffic_state.track_occupancy[track_id]

            track_status[track_id] = {
                'status': status,
                'occupying_train': occupying_train,
                'from_station': track.from_station,
                'to_station': track.to_station,
                'distance': track.distance,
                'max_speed': track.max_speed
            }

        return jsonify({
            'success': True,
            'tracks': track_status,
            'blocked_count': len(simulator.traffic_state.blocked_tracks),
            'total_tracks': len(network.tracks)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/metrics')
def get_metrics():
    """Get current performance metrics"""
    if not simulator:
        return jsonify({'error': 'Simulator not initialized'}), 500
    
    return jsonify({
        'current': simulator._get_current_metrics(),
        'history': simulator.metrics_history[-100:],  # Last 100 data points
        'performance': simulator.performance_data
    })


@app.route('/api/scenarios', methods=['GET', 'POST'])
def scenarios():
    """Get or create simulation scenarios"""
    if request.method == 'GET':
        # Return predefined scenarios
        scenarios = [
            {
                'id': 'rush_hour',
                'name': 'Rush Hour Traffic',
                'description': 'High traffic scenario with multiple express trains',
                'train_count': 8
            },
            {
                'id': 'disruption',
                'name': 'Track Disruption',
                'description': 'Single track failure causing delays',
                'train_count': 5
            },
            {
                'id': 'normal',
                'name': 'Normal Operations',
                'description': 'Standard traffic with mixed train types',
                'train_count': 6
            }
        ]
        return jsonify(scenarios)

    elif request.method == 'POST':
        # Create custom scenario
        try:
            data = request.get_json()
            # Implementation for creating custom scenarios
            return jsonify({'success': True, 'message': 'Scenario created'})
        except Exception as e:
            return jsonify({'error': str(e)}), 400


# Mobile App API Endpoints

@app.route('/api/mobile/auth', methods=['POST'])
def mobile_auth():
    """Authenticate mobile app users"""
    try:
        data = request.get_json()
        employee_id = data.get('employee_id')
        device_id = data.get('device_id')

        if not employee_id or not device_id:
            return jsonify({'error': 'Missing employee_id or device_id'}), 400

        # Simple authentication for demo - in production use proper auth
        auth_token = f"mobile_token_{employee_id}_{device_id}"

        return jsonify({
            'success': True,
            'auth_token': auth_token,
            'employee_id': employee_id,
            'permissions': ['report_incident', 'view_status'],
            'server_url': 'http://localhost:5000',
            'message': 'Authentication successful'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/mobile/incidents/report', methods=['POST'])
def report_mobile_incident():
    """Receive incident reports from mobile app"""
    try:
        data = request.get_json()
        incident = data.get('incident', {})

        print(f"📱 Received mobile incident report: {incident}")

        # Validate required fields
        required_fields = ['id', 'type', 'location']
        for field in required_fields:
            if field not in incident:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Process incident based on type and severity
        incident_type = incident['type']
        location = incident['location']
        severity = incident.get('severity', 'medium')
        train_number = incident.get('trainNumber')

        # Determine impact on current traffic
        affected_trains = []
        optimization_needed = False

        if incident_type in ['Signal Failure', 'Track Fault']:
            # Find trains near the incident location
            affected_trains = find_trains_near_location(
                location['latitude'],
                location['longitude'],
                radius_km=5.0 if severity == 'high' else 2.0
            )
            optimization_needed = len(affected_trains) > 0

            # Block affected tracks
            blocked_tracks = block_tracks_near_incident(location, incident_type)

        elif incident_type == 'Engine Breakdown' and train_number:
            # Find specific train
            if train_number in simulator.traffic_state.trains:
                affected_trains = [train_number]
                optimization_needed = True

                # Add delay to the broken train
                train = simulator.traffic_state.trains[train_number]
                train.delay += 30.0  # 30 minute delay for breakdown
                train.status = TrainStatus.DELAYED

        # Inject incident into simulation for real-time effects
        incident_record = simulator.inject_incident({
            'id': incident['id'],
            'type': incident_type,
            'location': location,
            'severity': severity,
            'trainNumber': train_number
        })

        # Apply emergency delays to affected trains
        emergency_delay = {'high': 30, 'medium': 15, 'low': 5}[severity]
        for train_id in affected_trains:
            if train_id in simulator.traffic_state.trains:
                train = simulator.traffic_state.trains[train_id]
                train.delay += emergency_delay
                if train.status == TrainStatus.RUNNING:
                    train.status = TrainStatus.DELAYED
                print(f"⚠️ Train {train_id} delayed by {emergency_delay} minutes due to incident")

        # Trigger AI optimization if needed
        optimization_result = None
        if optimization_needed and optimizer:
            print("🧠 Triggering emergency AI optimization...")
            optimization_result = optimizer.optimize_traffic(simulator.traffic_state)

            # Apply optimization results immediately
            if optimization_result['milp_solution']['status'] == 'Optimal':
                applied_changes = optimization_result.get('applied_changes', {})
                print(f"✅ AI optimization applied to {len(applied_changes)} trains")

        # Log incident in system
        incident_log = {
            'id': incident['id'],
            'type': incident_type,
            'location': location,
            'severity': severity,
            'affected_trains': affected_trains,
            'optimization_triggered': optimization_needed,
            'timestamp': datetime.now().isoformat(),
            'source': 'mobile_app',
            'train_number': train_number,
            'description': incident.get('description', ''),
            'reported_by': incident.get('reportedBy', 'unknown')
        }

        # Broadcast to connected clients
        socketio.emit('incident_reported', {
            'incident': incident_log,
            'optimization_result': optimization_result,
            'affected_trains': len(affected_trains)
        }, namespace='/simulation')

        response = {
            'success': True,
            'optimizationTriggered': optimization_needed,
            'affectedTrains': affected_trains,
            'emergencyDelayApplied': emergency_delay,
            'estimatedImpact': calculate_estimated_impact(incident_type, affected_trains),
            'message': f'Incident processed. {len(affected_trains)} trains affected, AI optimization {"triggered" if optimization_needed else "not needed"}.'
        }

        print(f"✅ Mobile incident {incident['id']} processed successfully")
        return jsonify(response)

    except Exception as e:
        print(f"❌ Error processing mobile incident: {str(e)}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/mobile/status', methods=['GET'])
def get_mobile_status():
    """Get system status for mobile app"""
    try:
        if not simulator:
            return jsonify({'error': 'System not initialized'}), 500

        status = {
            'system_online': True,
            'simulation_running': simulator.is_running,
            'simulation_paused': simulator.is_paused,
            'current_time': simulator.current_time,
            'active_trains': len([t for t in simulator.traffic_state.trains.values()
                                if t.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]]),
            'total_trains': len(simulator.traffic_state.trains),
            'server_time': datetime.now().isoformat(),
            'last_optimization': simulator.performance_data.get('optimization_calls', 0)
        }

        return jsonify(status)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/mobile/trains/nearby', methods=['POST'])
def get_nearby_trains():
    """Get trains near a specific location"""
    try:
        data = request.get_json()
        latitude = data.get('latitude')
        longitude = data.get('longitude')
        radius_km = data.get('radius_km', 10.0)

        if latitude is None or longitude is None:
            return jsonify({'error': 'Missing latitude or longitude'}), 400

        nearby_trains = find_trains_near_location(latitude, longitude, radius_km)

        train_details = []
        for train_id in nearby_trains:
            if train_id in simulator.traffic_state.trains:
                train = simulator.traffic_state.trains[train_id]
                train_details.append({
                    'id': train.id,
                    'status': train.status.value,
                    'delay': train.delay,
                    'priority': train.priority.value,
                    'current_station': train.get_current_station(),
                    'next_station': train.get_next_station()
                })

        return jsonify({
            'success': True,
            'trains': train_details,
            'count': len(train_details),
            'search_radius_km': radius_km
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


# WebSocket Events

@socketio.on('connect', namespace='/simulation')
def handle_connect():
    """Handle client connection"""
    session_id = request.sid
    active_sessions[session_id] = {
        'connected_at': datetime.now().isoformat(),
        'subscriptions': []
    }
    
    emit('connected', {
        'session_id': session_id,
        'server_time': datetime.now().isoformat(),
        'system_status': 'online' if simulator and simulator.is_running else 'offline'
    })
    
    print(f"Client connected: {session_id}")


@socketio.on('disconnect', namespace='/simulation')
def handle_disconnect():
    """Handle client disconnection"""
    session_id = request.sid
    if session_id in active_sessions:
        del active_sessions[session_id]
    
    print(f"Client disconnected: {session_id}")


@socketio.on('subscribe', namespace='/simulation')
def handle_subscribe(data):
    """Handle subscription to specific events"""
    session_id = request.sid
    event_type = data.get('event_type')
    
    if session_id in active_sessions:
        if event_type not in active_sessions[session_id]['subscriptions']:
            active_sessions[session_id]['subscriptions'].append(event_type)
        
        emit('subscription_confirmed', {
            'event_type': event_type,
            'status': 'subscribed'
        })


@socketio.on('unsubscribe', namespace='/simulation')
def handle_unsubscribe(data):
    """Handle unsubscription from events"""
    session_id = request.sid
    event_type = data.get('event_type')
    
    if session_id in active_sessions:
        if event_type in active_sessions[session_id]['subscriptions']:
            active_sessions[session_id]['subscriptions'].remove(event_type)
        
        emit('subscription_confirmed', {
            'event_type': event_type,
            'status': 'unsubscribed'
        })


@socketio.on('get_status', namespace='/simulation')
def handle_get_status():
    """Send current system status"""
    if simulator:
        status = {
            'simulation_running': simulator.is_running,
            'simulation_paused': simulator.is_paused,
            'current_time': simulator.current_time,
            'train_count': len(simulator.traffic_state.trains),
            'metrics': simulator._get_current_metrics()
        }
    else:
        status = {
            'simulation_running': False,
            'error': 'Simulator not initialized'
        }
    
    emit('status_update', status)


# Helper Functions for Mobile App Integration

def find_trains_near_location(lat, lng, radius_km=5.0):
    """Find trains within radius of incident location using haversine formula"""
    from math import radians, cos, sin, asin, sqrt

    def haversine_distance(lat1, lng1, lat2, lng2):
        # Convert to radians
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])

        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # Earth radius in km

        return c * r

    affected_trains = []

    if not simulator or not network:
        return affected_trains

    for train_id, train in simulator.traffic_state.trains.items():
        if train.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]:
            # Get train's current station
            if train.current_position < len(train.route):
                station_id = train.route[train.current_position]
                if station_id in network.stations:
                    station = network.stations[station_id]

                    distance = haversine_distance(
                        lat, lng,
                        station.latitude, station.longitude
                    )

                    if distance <= radius_km:
                        affected_trains.append(train_id)

    return affected_trains


def block_tracks_near_incident(location, incident_type):
    """Block tracks near incident location"""
    blocked_tracks = []

    if not network:
        return blocked_tracks

    # Find stations near incident
    nearby_stations = []
    for station_id, station in network.stations.items():
        distance = calculate_distance(
            location['latitude'], location['longitude'],
            station.latitude, station.longitude
        )
        if distance <= 2.0:  # Within 2km
            nearby_stations.append(station_id)

    # Block tracks connected to nearby stations
    for track_id, track in network.tracks.items():
        if (track.from_station in nearby_stations or
            track.to_station in nearby_stations):
            blocked_tracks.append(track_id)
            # Mark track as blocked in simulation
            if hasattr(simulator.traffic_state, 'blocked_tracks'):
                simulator.traffic_state.blocked_tracks.add(track_id)
            else:
                simulator.traffic_state.blocked_tracks = {track_id}

    print(f"🚫 Blocked {len(blocked_tracks)} tracks due to {incident_type}")
    return blocked_tracks


def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points using haversine formula"""
    from math import radians, cos, sin, asin, sqrt

    # Convert to radians
    lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])

    # Haversine formula
    dlat = lat2 - lat1
    dlng = lng2 - lng1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # Radius of earth in kilometers

    return c * r


def calculate_estimated_impact(incident_type, affected_trains):
    """Calculate estimated impact of incident"""
    if not affected_trains:
        return "minimal"

    impact_scores = {
        'Signal Failure': 3,
        'Track Fault': 4,
        'Engine Breakdown': 2,
        'Security Alert': 1,
        'Infrastructure Damage': 5,
        'Weather Related': 2,
        'Other': 1
    }

    base_impact = impact_scores.get(incident_type, 1)
    train_factor = min(len(affected_trains), 5)  # Cap at 5 trains

    total_impact = base_impact * train_factor

    if total_impact <= 3:
        return "low"
    elif total_impact <= 8:
        return "medium"
    else:
        return "high"


# Error Handlers

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404


@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500


if __name__ == '__main__':
    # Initialize the system
    initialize_system()
    
    # Add comprehensive sample trains for realistic demo
    sample_trains = [
        # Express Trains
        Train(
            id="12951",
            route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="12952",
            route=["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="12953",
            route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[2.0, 2.5, 12.5, 16.5, 21.5, 31.5],
            current_position=0,
            current_time=0.0
        ),
        # Passenger Trains
        Train(
            id="19037",
            route=["DEL", "GZB", "ALD", "JHS", "BPL"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[1.0, 1.5, 11.5, 15.5, 20.5],
            current_position=0,
            current_time=0.0,
            delay=3.0  # Start with some delay
        ),
        Train(
            id="19038",
            route=["BPL", "JHS", "ALD", "GZB", "DEL"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[0, 5.0, 9.0, 19.0, 19.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="19039",
            route=["NGP", "BSL", "MMR", "NK", "KYN", "CSTM"],
            priority=TrainPriority.PASSENGER,
            scheduled_times=[0, 4.5, 8.5, 10.5, 15.5, 16.0],
            current_position=0,
            current_time=0.0
        ),
        # Freight Trains
        Train(
            id="50001",
            route=["DEL", "GZB", "ALD", "JHS"],
            priority=TrainPriority.FREIGHT,
            scheduled_times=[3.0, 3.5, 13.5, 17.5],
            current_position=0,
            current_time=0.0,
            delay=8.0  # Freight typically has more delays
        ),
        Train(
            id="50002",
            route=["NGP", "BSL", "MMR", "NK"],
            priority=TrainPriority.FREIGHT,
            scheduled_times=[1.0, 5.5, 9.5, 11.5],
            current_position=0,
            current_time=0.0,
            delay=5.0
        ),
        # Maintenance Train
        Train(
            id="87001",
            route=["BPL", "NGP", "BSL"],
            priority=TrainPriority.MAINTENANCE,
            scheduled_times=[4.0, 14.0, 18.5],
            current_position=0,
            current_time=0.0
        ),

        # Additional trains to utilize more tracks
        Train(
            id="12345",
            route=["DEL", "AGC", "GWL", "JHS", "VGLB"],  # Using branch line
            priority=TrainPriority.EXPRESS,
            scheduled_times=[1.5, 3.5, 5.0, 6.5, 7.0],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="22345",
            route=["BPL", "ET", "NGP", "WR", "CD"],  # Using alternate route
            priority=TrainPriority.PASSENGER,
            scheduled_times=[2.0, 3.0, 5.5, 7.0, 9.5],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="32345",
            route=["NK", "IGP", "LNL", "PUNE"],  # Using western branch
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0.5, 2.0, 3.5, 5.0],
            current_position=0,
            current_time=0.0
        ),
        Train(
            id="42345",
            route=["KYN", "LNL", "IGP", "NK", "MMR"],  # Reverse western route
            priority=TrainPriority.PASSENGER,
            scheduled_times=[1.0, 2.5, 4.0, 5.5, 7.0],
            current_position=0,
            current_time=0.0,
            delay=2.0
        ),
        Train(
            id="52345",
            route=["BPL", "HBJ"],  # Short route using multiple track
            priority=TrainPriority.FREIGHT,
            scheduled_times=[0.5, 1.0],
            current_position=0,
            current_time=0.0,
            delay=1.0
        ),
        Train(
            id="62345",
            route=["MMR", "PUNE"],  # Direct Mumbai-Pune route
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 3.5],
            current_position=0,
            current_time=0.0
        )
    ]
    
    for train in sample_trains:
        simulator.add_train(train)
    
    print("Starting Flask server...")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
