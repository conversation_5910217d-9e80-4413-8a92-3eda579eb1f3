"""
Interactive Train Movement Simulator
Real-time visualization and scenario testing for railway traffic control
"""

import pygame
import numpy as np
import time
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import threading
import queue
from datetime import datetime, timedelta

from models.railway_network import RailwayNetwork, Station, Track
from models.ai_optimizer import Train, TrafficState, HybridOptimizer, TrainStatus, TrainPriority


class SimulationMode(Enum):
    REAL_TIME = "real_time"
    ACCELERATED = "accelerated"
    STEP_BY_STEP = "step_by_step"


@dataclass
class SimulationConfig:
    """Simulation configuration parameters"""
    mode: SimulationMode = SimulationMode.REAL_TIME
    speed_multiplier: float = 1.0
    auto_optimize: bool = True
    show_delays: bool = True
    show_capacity: bool = True
    record_metrics: bool = True


class TrainSimulator:
    """Core train movement simulation engine"""
    
    def __init__(self, network: RailwayNetwork, config: SimulationConfig = None):
        self.network = network
        self.config = config or SimulationConfig()
        self.traffic_state = TrafficState(network)
        self.optimizer = HybridOptimizer(network)
        
        # Simulation state
        self.current_time = 0.0
        self.is_running = False
        self.is_paused = False
        self.step_size = 0.1  # minutes
        
        # Event system
        self.event_queue = queue.Queue()
        self.event_handlers = {}
        
        # Metrics collection
        self.metrics_history = []
        self.performance_data = {
            'throughput': [],
            'average_delay': [],
            'capacity_utilization': [],
            'optimization_calls': 0
        }
        
        # Visual state for rendering
        self.train_positions = {}  # train_id -> (lat, lon, progress)
        self.train_animations = {}  # train_id -> animation state
        
    def add_train(self, train: Train):
        """Add train to simulation"""
        self.traffic_state.add_train(train)
        self.train_positions[train.id] = self._get_initial_position(train)
        self.train_animations[train.id] = {
            'target_position': None,
            'animation_progress': 0.0,
            'moving': False
        }
        
    def _get_initial_position(self, train: Train) -> Tuple[float, float, float]:
        """Get initial position for train"""
        if train.route and train.current_position < len(train.route):
            station_id = train.route[train.current_position]
            station = self.network.stations[station_id]
            return (station.latitude, station.longitude, 0.0)
        return (0.0, 0.0, 0.0)
    
    def start_simulation(self):
        """Start the simulation"""
        self.is_running = True
        self.is_paused = False
        
        # Start simulation thread
        self.sim_thread = threading.Thread(target=self._simulation_loop)
        self.sim_thread.daemon = True
        self.sim_thread.start()
        
        self._emit_event('simulation_started', {'time': self.current_time})
    
    def pause_simulation(self):
        """Pause/unpause simulation"""
        self.is_paused = not self.is_paused
        self._emit_event('simulation_paused' if self.is_paused else 'simulation_resumed', 
                        {'time': self.current_time})
    
    def stop_simulation(self):
        """Stop the simulation"""
        self.is_running = False
        self._emit_event('simulation_stopped', {'time': self.current_time})
    
    def step_simulation(self):
        """Advance simulation by one step"""
        if not self.is_running:
            self.start_simulation()
        
        self._update_simulation()
        self._emit_event('simulation_stepped', {'time': self.current_time})
    
    def _simulation_loop(self):
        """Main simulation loop"""
        last_time = time.time()
        
        while self.is_running:
            current_real_time = time.time()
            dt = current_real_time - last_time
            last_time = current_real_time
            
            if not self.is_paused:
                # Update simulation time based on speed multiplier
                if self.config.mode == SimulationMode.REAL_TIME:
                    self.current_time += dt * self.config.speed_multiplier / 60.0  # Convert to minutes
                elif self.config.mode == SimulationMode.ACCELERATED:
                    self.current_time += self.step_size * self.config.speed_multiplier
                
                self._update_simulation()
            
            # Control simulation speed
            if self.config.mode == SimulationMode.STEP_BY_STEP:
                break
            
            time.sleep(0.1)  # 10 FPS update rate
    
    def _update_simulation(self):
        """Update simulation state with enhanced multi-train coordination"""
        # Sort trains by priority for processing order (higher priority first)
        trains_by_priority = sorted(
            [t for t in self.traffic_state.trains.values() if t.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]],
            key=lambda t: (t.priority.value, t.delay)  # Lower priority value = higher priority
        )

        # Update trains in priority order
        for train in trains_by_priority:
            self._update_train(train)

        # Handle station conflicts and optimize platform assignments
        self._resolve_station_conflicts()
        
        # Run AI optimization if enabled
        if self.config.auto_optimize and self.current_time % 5.0 < self.step_size:  # Every 5 minutes
            self._run_optimization()
        
        # Update metrics
        if self.config.record_metrics:
            self._update_metrics()
        
        # Emit update event
        self._emit_event('simulation_updated', {
            'time': self.current_time,
            'trains': {tid: self._serialize_train(train) 
                      for tid, train in self.traffic_state.trains.items()},
            'metrics': self._get_current_metrics()
        })
    
    def _update_train(self, train: Train):
        """Update individual train state"""
        if train.status == TrainStatus.COMPLETED:
            return
        
        # Check if train should move to next station
        if (train.current_position < len(train.scheduled_times) and 
            self.current_time >= train.scheduled_times[train.current_position] + train.delay):
            
            if train.current_position + 1 < len(train.route):
                # Move to next station
                self._move_train_to_next_station(train)
            else:
                # Train completed journey
                train.status = TrainStatus.COMPLETED
                self._emit_event('train_completed', {'train_id': train.id, 'time': self.current_time})
    
    def _move_train_to_next_station(self, train: Train):
        """Move train to next station in route with enhanced logic"""
        current_station = train.route[train.current_position]
        next_station = train.route[train.current_position + 1]

        # Find the correct track ID - check both directions
        track_id = None
        for tid, track in self.network.tracks.items():
            if ((track.from_station == current_station and track.to_station == next_station) or
                (track.from_station == next_station and track.to_station == current_station)):
                track_id = tid
                break

        if not track_id:
            # Create a virtual track ID for simulation
            track_id = f"{current_station}-{next_station}"
            # Initialize track occupancy if not exists
            if track_id not in self.traffic_state.track_occupancy:
                self.traffic_state.track_occupancy[track_id] = None

        # Check if track is blocked due to incident
        if hasattr(self.traffic_state, 'blocked_tracks') and track_id in self.traffic_state.blocked_tracks:
            # Track is blocked, train must pause at current safe node (station)
            print(f"🚫 Track {track_id} blocked for train {train.id}, pausing at safe node...")

            # Train pauses at current station (safe node)
            train.status = TrainStatus.WAITING
            train.delay += 5.0  # Add delay for waiting

            # Try to find alternative route
            alternative_route = self.traffic_state.find_alternative_route(train)
            if alternative_route != train.route:
                # Update train route
                old_route = train.route.copy()
                train.route = alternative_route
                train.delay += 15.0  # Add delay for rerouting
                train.status = TrainStatus.DELAYED

                self._emit_event('train_rerouted', {
                    'train_id': train.id,
                    'old_route': old_route,
                    'new_route': train.route,
                    'reason': 'track_blocked',
                    'time': self.current_time,
                    'safe_node': current_station
                })

                print(f"🔄 Train {train.id} rerouted from {current_station}: {' → '.join(train.route)}")

                # Trigger immediate optimization for better rerouting
                if hasattr(self, 'optimizer') and self.optimizer:
                    print(f"🧠 Triggering emergency optimization for train {train.id}")
                    self._run_optimization()

                return  # Try again next step with new route
            else:
                # No alternative route, train must wait at current station
                self._emit_event('train_delayed', {
                    'train_id': train.id,
                    'delay': train.delay,
                    'reason': 'track_blocked_no_alternative',
                    'time': self.current_time,
                    'waiting_at': current_station
                })
                print(f"⏸️ Train {train.id} waiting at {current_station} - no alternative route available")
                return

        # Check track availability (not occupied and not blocked)
        if self.traffic_state.is_track_available(track_id):
            # Track is free, move train
            self.traffic_state.track_occupancy[track_id] = train.id
            train.current_position += 1
            train.status = TrainStatus.RUNNING

            # Update visual position
            self._update_train_visual_position(train)

            # Calculate travel time
            if track_id in self.network.tracks:
                track = self.network.tracks[track_id]
                travel_time = track.distance / track.max_speed * 60  # Convert to minutes
                # Add delay for congestion, gradient, etc.
                delay_factor = 1.0 + (track.gradient / 100.0) * 0.1
                travel_time *= delay_factor
            else:
                # Default travel time for virtual tracks
                travel_time = 30.0  # 30 minutes default

            # Release track after travel time
            threading.Timer(travel_time / self.config.speed_multiplier,
                          self._release_track, args=[track_id]).start()

            self._emit_event('train_moved', {
                'train_id': train.id,
                'from_station': current_station,
                'to_station': next_station,
                'track_id': track_id,
                'time': self.current_time
            })
        else:
            # Track occupied - apply intelligent conflict resolution
            occupying_train_id = self.traffic_state.track_occupancy.get(track_id)

            if occupying_train_id and occupying_train_id in self.traffic_state.trains:
                occupying_train = self.traffic_state.trains[occupying_train_id]

                # Priority-based conflict resolution
                if train.priority.value < occupying_train.priority.value:  # Lower value = higher priority
                    print(f"🚨 Priority conflict: Train {train.id} (priority {train.priority.value}) waiting for train {occupying_train_id} (priority {occupying_train.priority.value})")

                    # Higher priority train gets preference - add minimal delay
                    train.delay += self.step_size * 0.5
                    train.status = TrainStatus.DELAYED

                    # Try to expedite the occupying lower priority train
                    if occupying_train.status == TrainStatus.RUNNING:
                        occupying_train.delay = max(0, occupying_train.delay - 1.0)  # Reduce delay to clear track faster
                else:
                    # Lower or equal priority train waits normally
                    train.delay += self.step_size * 2  # Double delay for lower priority
                    train.status = TrainStatus.DELAYED
            else:
                # Track blocked by incident
                train.delay += self.step_size
                train.status = TrainStatus.DELAYED

            self._emit_event('train_delayed', {
                'train_id': train.id,
                'delay': train.delay,
                'reason': 'track_occupied' if occupying_train_id else 'track_blocked',
                'conflicting_train': occupying_train_id,
                'priority_conflict': occupying_train_id and train.priority.value < self.traffic_state.trains[occupying_train_id].priority.value,
                'time': self.current_time
            })
    
    def _release_track(self, track_id: str):
        """Release track occupancy"""
        if track_id in self.traffic_state.track_occupancy:
            train_id = self.traffic_state.track_occupancy[track_id]
            self.traffic_state.track_occupancy[track_id] = None
            
            if train_id:
                self._emit_event('track_released', {
                    'track_id': track_id,
                    'train_id': train_id,
                    'time': self.current_time
                })

                print(f"✅ Track {track_id} released by train {train_id}")

                # Check if any waiting trains can now use this track
                self._process_waiting_trains_for_track(track_id)

    def _process_waiting_trains_for_track(self, released_track_id: str):
        """Process waiting trains that might be able to use the released track"""
        waiting_trains = []

        # Find trains that are waiting and might need this track
        for train in self.traffic_state.trains.values():
            if train.status == TrainStatus.DELAYED and train.current_position < len(train.route) - 1:
                current_station = train.route[train.current_position]
                next_station = train.route[train.current_position + 1]
                needed_track = f"{current_station}-{next_station}"

                if needed_track == released_track_id:
                    waiting_trains.append(train)

        if waiting_trains:
            # Sort by priority (lower value = higher priority) and delay
            waiting_trains.sort(key=lambda t: (t.priority.value, -t.delay))

            # Give the track to the highest priority train with most delay
            priority_train = waiting_trains[0]
            print(f"🎯 Assigning released track {released_track_id} to priority train {priority_train.id} (priority: {priority_train.priority.value}, delay: {priority_train.delay:.1f})")

            # Reduce delay for getting priority access
            priority_train.delay = max(0, priority_train.delay - 2.0)

            # Emit priority assignment event
            self._emit_event('priority_track_assignment', {
                'track_id': released_track_id,
                'train_id': priority_train.id,
                'priority': priority_train.priority.value,
                'delay_reduction': 2.0,
                'waiting_trains_count': len(waiting_trains),
                'time': self.current_time
            })

    def _update_train_visual_position(self, train: Train):
        """Update train visual position for animation with realistic movement"""
        if train.current_position < len(train.route):
            station_id = train.route[train.current_position]
            station = self.network.stations[station_id]

            # Calculate realistic position based on time and schedule
            if train.current_position > 0:
                prev_station_id = train.route[train.current_position - 1]
                prev_station = self.network.stations[prev_station_id]

                # Calculate progress based on time elapsed since leaving previous station
                if train.current_position < len(train.scheduled_times):
                    scheduled_departure = train.scheduled_times[train.current_position - 1] + train.delay
                    scheduled_arrival = train.scheduled_times[train.current_position] + train.delay
                    travel_time = scheduled_arrival - scheduled_departure

                    if travel_time > 0:
                        time_elapsed = self.current_time - scheduled_departure
                        progress = max(0.0, min(1.0, time_elapsed / travel_time))
                    else:
                        progress = 1.0
                else:
                    progress = 1.0

                # Smooth interpolation between stations
                lat = prev_station.latitude + (station.latitude - prev_station.latitude) * progress
                lon = prev_station.longitude + (station.longitude - prev_station.longitude) * progress

                self.train_positions[train.id] = (lat, lon, progress)
                self.train_animations[train.id] = {
                    'from_station': prev_station_id,
                    'to_station': station_id,
                    'target_position': (station.latitude, station.longitude),
                    'animation_progress': progress,
                    'moving': progress < 1.0
                }
            else:
                # Train at starting station
                self.train_positions[train.id] = (station.latitude, station.longitude, 1.0)
                self.train_animations[train.id] = {
                    'from_station': None,
                    'to_station': station_id,
                    'target_position': (station.latitude, station.longitude),
                    'animation_progress': 1.0,
                    'moving': False
                }
    
    def _run_optimization(self):
        """Run AI optimization and apply results"""
        try:
            print(f"🧠 Running AI optimization at time {self.current_time:.1f}")
            result = self.optimizer.optimize_traffic(self.traffic_state)
            self.performance_data['optimization_calls'] += 1

            # Log optimization results
            if result['milp_solution']['status'] == 'Optimal':
                improvement = result['milp_solution'].get('metrics', {}).get('improvement', 0)
                print(f"✅ Optimization completed - Delay improvement: {improvement:.1f} minutes")

                # Apply any additional optimization effects
                self._apply_optimization_effects(result)
            else:
                print(f"⚠️ Optimization status: {result['milp_solution']['status']}")

            self._emit_event('optimization_completed', {
                'result': result,
                'time': self.current_time,
                'trains_affected': len(result.get('applied_changes', {}))
            })
        except Exception as e:
            print(f"❌ Optimization failed: {str(e)}")
            self._emit_event('optimization_failed', {
                'error': str(e),
                'time': self.current_time
            })

    def _apply_optimization_effects(self, optimization_result):
        """Apply additional optimization effects to simulation"""
        if 'applied_changes' not in optimization_result:
            return

        changes_applied = 0
        for train_id, changes in optimization_result['applied_changes'].items():
            if train_id in self.traffic_state.trains:
                train = self.traffic_state.trains[train_id]

                # Apply optimized delay
                if 'optimized_delay' in changes:
                    old_delay = train.delay
                    train.delay = changes['optimized_delay']
                    if old_delay != train.delay:
                        changes_applied += 1
                        print(f"🔧 Train {train_id}: delay {old_delay:.1f} → {train.delay:.1f} min")

        if changes_applied > 0:
            print(f"✅ Applied optimization to {changes_applied} trains")
    
    def _update_metrics(self):
        """Update performance metrics"""
        metrics = self._get_current_metrics()
        self.metrics_history.append({
            'time': self.current_time,
            **metrics
        })
        
        # Update performance data arrays
        self.performance_data['throughput'].append(metrics['throughput'])
        self.performance_data['average_delay'].append(metrics['average_delay'])
        self.performance_data['capacity_utilization'].append(metrics['capacity_utilization'])
        
        # Keep only last 1000 data points
        for key in ['throughput', 'average_delay', 'capacity_utilization']:
            if len(self.performance_data[key]) > 1000:
                self.performance_data[key] = self.performance_data[key][-1000:]
    
    def _get_current_metrics(self) -> Dict[str, float]:
        """Get current performance metrics"""
        trains = list(self.traffic_state.trains.values())
        
        if not trains:
            return {
                'throughput': 0.0,
                'average_delay': 0.0,
                'capacity_utilization': 0.0,
                'active_trains': 0
            }
        
        completed_trains = [t for t in trains if t.status == TrainStatus.COMPLETED]
        active_trains = [t for t in trains if t.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]]
        
        throughput = len(completed_trains)
        average_delay = np.mean([t.delay for t in trains]) if trains else 0.0
        
        # Calculate capacity utilization
        total_capacity = sum(station.capacity for station in self.network.stations.values())
        used_capacity = sum(len(occupants) for occupants in self.traffic_state.station_occupancy.values())
        capacity_utilization = used_capacity / total_capacity if total_capacity > 0 else 0.0
        
        return {
            'throughput': throughput,
            'average_delay': average_delay,
            'capacity_utilization': capacity_utilization,
            'active_trains': len(active_trains)
        }
    
    def _serialize_train(self, train: Train) -> Dict:
        """Serialize train for JSON output with enhanced visual data"""
        visual_pos = self.train_positions.get(train.id, (0, 0, 0))
        animation_data = self.train_animations.get(train.id, {})

        # Get current and next station info
        current_station = train.get_current_station()
        next_station = train.get_next_station()

        # Determine current track
        current_track = None
        if current_station and next_station:
            current_track = f"{current_station}-{next_station}"
        elif train.current_position > 0 and train.current_position < len(train.route):
            # Train is between stations
            prev_station = train.route[train.current_position - 1]
            curr_station = train.route[train.current_position]
            current_track = f"{prev_station}-{curr_station}"

        return {
            'id': train.id,
            'route': train.route,
            'priority': train.priority.value,
            'current_position': train.current_position,
            'current_time': train.current_time,
            'delay': train.delay,
            'status': train.status.value,
            'visual_position': visual_pos,
            'animation': animation_data,
            'current_station': current_station,
            'next_station': next_station,
            'current_track': current_track,
            'progress_to_next': animation_data.get('animation_progress', 0.0),
            'is_moving': animation_data.get('moving', False),
            'scheduled_times': train.scheduled_times
        }
    
    def _emit_event(self, event_type: str, data: Dict):
        """Emit simulation event"""
        event = {
            'type': event_type,
            'timestamp': datetime.now().isoformat(),
            'simulation_time': self.current_time,
            'data': data
        }
        
        self.event_queue.put(event)
        
        # Call registered handlers
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    handler(event)
                except Exception as e:
                    print(f"Error in event handler: {e}")
    
    def register_event_handler(self, event_type: str, handler):
        """Register event handler"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
    
    def get_events(self) -> List[Dict]:
        """Get all pending events"""
        events = []
        while not self.event_queue.empty():
            try:
                events.append(self.event_queue.get_nowait())
            except queue.Empty:
                break
        return events
    
    def create_scenario(self, scenario_name: str, trains: List[Train] = None,
                       disruptions: List[Dict] = None) -> Dict:
        """Create a simulation scenario with predefined or custom trains"""

        # If no trains provided, generate based on scenario type
        if trains is None:
            trains = self._generate_scenario_trains(scenario_name)

        scenario = {
            'name': scenario_name,
            'created_at': datetime.now().isoformat(),
            'trains': [asdict(train) for train in trains],
            'disruptions': disruptions or [],
            'network_stats': self.network.get_network_stats()
        }

        return scenario

    def _generate_scenario_trains(self, scenario_name: str) -> List[Train]:
        """Generate trains based on scenario type"""
        from models.ai_optimizer import Train, TrainPriority, TrainStatus

        trains = []

        if scenario_name == "rush_hour":
            # High traffic scenario
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5]),
                ("12952", ["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS", "BPL"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5, 13.5]),
                ("19038", ["BPL", "ET", "NGP", "BSL", "MMR"], TrainPriority.PASSENGER, [0, 2.0, 4.5, 8.5, 12.5]),
                ("50001", ["DEL", "GZB", "ALD", "JHS"], TrainPriority.FREIGHT, [2.0, 2.5, 12.5, 16.5]),
                ("50002", ["NGP", "WR", "BSL", "MMR"], TrainPriority.FREIGHT, [1.0, 3.5, 7.5, 11.5]),
                ("12953", ["PUNE", "LNL", "IGP", "NK", "MMR"], TrainPriority.EXPRESS, [0, 1.5, 3.5, 5.5, 7.5]),
                ("19039", ["KYN", "LNL", "PUNE"], TrainPriority.PASSENGER, [0, 2.0, 4.0])
            ]

        elif scenario_name == "disruption":
            # Track disruption scenario with delays
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5]),
                ("50001", ["BPL", "ET", "NGP"], TrainPriority.FREIGHT, [0, 2.0, 6.0]),
                ("87001", ["NGP", "BSL", "MMR"], TrainPriority.MAINTENANCE, [2.0, 6.0, 10.0]),
                ("12952", ["CSTM", "KYN", "LNL", "PUNE"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.0])
            ]

        else:  # normal operations
            train_configs = [
                ("12951", ["DEL", "GZB", "ALD", "JHS", "BPL", "NGP"], TrainPriority.EXPRESS, [0, 0.5, 10.5, 14.5, 19.5, 29.5]),
                ("12952", ["CSTM", "KYN", "NK", "MMR", "BSL"], TrainPriority.EXPRESS, [0, 1.0, 3.0, 5.5, 8.5]),
                ("19037", ["DEL", "AGC", "GWL", "JHS"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5, 8.5]),
                ("50001", ["NGP", "BSL", "MMR"], TrainPriority.FREIGHT, [0, 4.0, 8.0]),
                ("19038", ["PUNE", "LNL", "KYN", "CSTM"], TrainPriority.PASSENGER, [0, 1.5, 3.5, 5.5]),
                ("87001", ["BPL", "ET", "NGP"], TrainPriority.MAINTENANCE, [3.0, 5.0, 9.0])
            ]

        # Create train objects
        for train_id, route, priority, scheduled_times in train_configs:
            delay = 0.0
            if scenario_name == "disruption":
                delay = random.uniform(0, 15)  # Random delays for disruption scenario
            elif scenario_name == "rush_hour":
                delay = random.uniform(0, 8)   # Some delays for rush hour

            train = Train(
                id=train_id,
                route=route,
                priority=priority,
                scheduled_times=scheduled_times,
                current_position=0,
                current_time=0.0,
                delay=delay,
                status=TrainStatus.SCHEDULED
            )
            trains.append(train)

        return trains
    
    def load_scenario(self, scenario: Dict):
        """Load a simulation scenario"""
        # Clear current state
        self.traffic_state = TrafficState(self.network)
        self.train_positions = {}
        self.train_animations = {}
        self.current_time = 0.0
        
        # Load trains
        for train_data in scenario['trains']:
            train_data['priority'] = TrainPriority(train_data['priority'])
            train_data['status'] = TrainStatus(train_data['status'])
            train = Train(**train_data)
            self.add_train(train)
        
        self._emit_event('scenario_loaded', {
            'scenario_name': scenario['name'],
            'train_count': len(scenario['trains'])
        })
    
    def inject_incident(self, incident_data: Dict):
        """Inject an incident into the simulation"""
        incident_type = incident_data.get('type', 'Other')
        location = incident_data.get('location', {})
        severity = incident_data.get('severity', 'medium')

        print(f"🚨 Injecting incident: {incident_type} at {location}")

        # Find tracks near incident location
        affected_tracks = self._find_tracks_near_location(location)

        # Block affected tracks
        for track_id in affected_tracks:
            self.traffic_state.block_track(track_id, f"{incident_type} incident")

        # Add incident to traffic state
        if not hasattr(self.traffic_state, 'incidents'):
            self.traffic_state.incidents = []

        incident_record = {
            'id': incident_data.get('id', f"INC_{int(self.current_time)}"),
            'type': incident_type,
            'location': location,
            'severity': severity,
            'affected_tracks': affected_tracks,
            'timestamp': self.current_time,
            'active': True
        }

        self.traffic_state.incidents.append(incident_record)

        # Emit incident event
        self._emit_event('incident_injected', {
            'incident': incident_record,
            'affected_tracks': affected_tracks,
            'time': self.current_time
        })

        # Trigger immediate optimization
        if self.optimizer:
            self._run_optimization()

        return incident_record

    def _find_tracks_near_location(self, location: Dict) -> List[str]:
        """Find tracks near incident location"""
        if not location or 'latitude' not in location or 'longitude' not in location:
            return []

        lat = location['latitude']
        lng = location['longitude']
        affected_tracks = []

        # Find stations within 2km of incident
        nearby_stations = []
        for station_id, station in self.network.stations.items():
            distance = self._calculate_distance(lat, lng, station.latitude, station.longitude)
            if distance <= 2.0:  # Within 2km
                nearby_stations.append(station_id)

        # Find tracks connected to nearby stations
        for track_id, track in self.network.tracks.items():
            if (track.from_station in nearby_stations or
                track.to_station in nearby_stations):
                affected_tracks.append(track_id)

        return affected_tracks

    def _calculate_distance(self, lat1: float, lng1: float, lat2: float, lng2: float) -> float:
        """Calculate distance between two points using haversine formula"""
        from math import radians, cos, sin, asin, sqrt

        # Convert to radians
        lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])

        # Haversine formula
        dlat = lat2 - lat1
        dlng = lng2 - lng1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
        c = 2 * asin(sqrt(a))
        r = 6371  # Radius of earth in kilometers

        return c * r

    def export_metrics(self, filepath: str):
        """Export metrics to JSON file"""
        export_data = {
            'simulation_config': asdict(self.config),
            'network_stats': self.network.get_network_stats(),
            'metrics_history': self.metrics_history,
            'performance_data': self.performance_data,
            'export_time': datetime.now().isoformat()
        }

        with open(filepath, 'w') as f:
            json.dump(export_data, f, indent=2)

    def _resolve_station_conflicts(self):
        """Resolve conflicts when multiple trains arrive at the same station"""
        station_arrivals = {}

        # Group trains by their current station
        for train in self.traffic_state.trains.values():
            if train.status in [TrainStatus.RUNNING, TrainStatus.DELAYED] and train.current_position < len(train.route):
                current_station = train.route[train.current_position]
                if current_station not in station_arrivals:
                    station_arrivals[current_station] = []
                station_arrivals[current_station].append(train)

        # Resolve conflicts at stations with multiple trains
        for station_id, trains in station_arrivals.items():
            if len(trains) > 1:
                # Sort by priority and delay
                trains.sort(key=lambda t: (t.priority.value, -t.delay))

                # Give priority to express trains and heavily delayed trains
                for i, train in enumerate(trains):
                    if i == 0:  # Highest priority train
                        # Reduce delay for priority handling
                        train.delay = max(0, train.delay - 1.0)
                    else:
                        # Add small delay for lower priority trains
                        train.delay += 0.5 * i

                print(f"🚉 Station conflict resolved at {station_id}: Priority given to train {trains[0].id}")

                self._emit_event('station_conflict_resolved', {
                    'station_id': station_id,
                    'trains': [t.id for t in trains],
                    'priority_train': trains[0].id,
                    'time': self.current_time
                })


if __name__ == "__main__":
    # Test the simulator
    from models.railway_network import create_sample_network
    
    network = create_sample_network()
    config = SimulationConfig(mode=SimulationMode.ACCELERATED, speed_multiplier=10.0)
    simulator = TrainSimulator(network, config)
    
    # Add sample trains
    train1 = Train(
        id="12951",
        route=["DEL", "GZB", "ALD", "JHS", "BPL", "NGP", "BSL", "CSTM"],
        priority=TrainPriority.EXPRESS,
        scheduled_times=[0, 0.5, 10.5, 14.5, 19.5, 29.5, 34.5, 42.5],
        current_position=0,
        current_time=0.0
    )
    
    train2 = Train(
        id="12952",
        route=["CSTM", "KYN", "NK", "MMR", "BSL", "NGP", "BPL", "JHS", "ALD", "GZB", "DEL"],
        priority=TrainPriority.EXPRESS,
        scheduled_times=[0, 1.0, 3.0, 5.5, 8.5, 12.5, 22.5, 27.5, 31.5, 42.0, 42.5],
        current_position=0,
        current_time=0.0
    )
    
    simulator.add_train(train1)
    simulator.add_train(train2)
    
    # Register event handler
    def print_events(event):
        print(f"Event: {event['type']} at {event['simulation_time']:.1f}min")
    
    simulator.register_event_handler('train_moved', print_events)
    simulator.register_event_handler('train_completed', print_events)
    
    # Run simulation for a short time
    simulator.start_simulation()
    time.sleep(5)  # Run for 5 seconds
    simulator.stop_simulation()
    
    print("Final metrics:", simulator._get_current_metrics())
