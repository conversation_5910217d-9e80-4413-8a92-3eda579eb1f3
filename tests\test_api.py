"""
Comprehensive Test Suite for Flask API
Tests all API endpoints and WebSocket functionality
"""

import pytest
import json
import sys
import os
from unittest.mock import Mock, patch

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app import app, socketio
from models.railway_network import create_sample_network
from models.ai_optimizer import Train, TrainPriority, TrainStatus


class TestFlaskAPI:
    """Test Flask API endpoints"""
    
    def setup_method(self):
        """Setup test fixtures"""
        app.config['TESTING'] = True
        self.client = app.test_client()
        self.ctx = app.app_context()
        self.ctx.push()
    
    def teardown_method(self):
        """Cleanup after tests"""
        self.ctx.pop()
    
    def test_index_route(self):
        """Test main index route"""
        response = self.client.get('/')
        assert response.status_code == 200
        assert b'AI-Powered Train Traffic Control' in response.data
    
    def test_get_trains_empty(self):
        """Test getting trains when none exist"""
        response = self.client.get('/api/trains')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert isinstance(data, dict)
        assert len(data) == 0
    
    @patch('backend.app.simulator')
    def test_get_trains_with_data(self, mock_simulator):
        """Test getting trains with mock data"""
        # Mock train data
        mock_train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        mock_simulator.traffic_state.trains = {"TEST001": mock_train}
        
        response = self.client.get('/api/trains')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert "TEST001" in data
        assert data["TEST001"]["id"] == "TEST001"
        assert data["TEST001"]["priority"] == "EXPRESS"
    
    def test_add_train_valid(self):
        """Test adding a valid train"""
        train_data = {
            "id": "NEW001",
            "route": ["DEL", "GZB", "ALD"],
            "priority": "EXPRESS",
            "scheduled_times": [0, 0.5, 10.5],
            "delay": 0.0
        }
        
        response = self.client.post('/api/trains', 
                                  data=json.dumps(train_data),
                                  content_type='application/json')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] == True
        assert data['message'] == 'Train added successfully'
    
    def test_add_train_invalid(self):
        """Test adding invalid train data"""
        invalid_data = {
            "id": "INVALID",
            # Missing required fields
        }
        
        response = self.client.post('/api/trains',
                                  data=json.dumps(invalid_data),
                                  content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_add_train_missing_json(self):
        """Test adding train without JSON data"""
        response = self.client.post('/api/trains')
        assert response.status_code == 400
    
    @patch('backend.app.optimizer')
    def test_run_optimization(self, mock_optimizer):
        """Test running optimization"""
        # Mock optimization result
        mock_optimizer.optimize_traffic.return_value = {
            'rl_action': 5,
            'milp_solution': {'status': 'Optimal'},
            'optimization_time': 1.5,
            'recommendations': ['Reduce delay for train T001']
        }
        
        response = self.client.post('/api/optimization/run')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['success'] == True
        assert 'result' in data
        assert data['result']['milp_solution']['status'] == 'Optimal'
    
    def test_run_optimization_no_optimizer(self):
        """Test optimization when optimizer is not available"""
        with patch('backend.app.optimizer', None):
            response = self.client.post('/api/optimization/run')
            assert response.status_code == 500
            
            data = json.loads(response.data)
            assert 'error' in data
    
    @patch('backend.app.simulator')
    def test_get_metrics(self, mock_simulator):
        """Test getting simulation metrics"""
        # Mock metrics
        mock_simulator._get_current_metrics.return_value = {
            'throughput': 5,
            'average_delay': 8.5,
            'capacity_utilization': 0.65,
            'active_trains': 3
        }
        
        response = self.client.get('/api/metrics')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert data['throughput'] == 5
        assert data['average_delay'] == 8.5
        assert data['capacity_utilization'] == 0.65
        assert data['active_trains'] == 3
    
    @patch('backend.app.simulator')
    def test_simulation_control(self, mock_simulator):
        """Test simulation control endpoints"""
        # Test start
        response = self.client.post('/api/simulation/start')
        assert response.status_code == 200
        mock_simulator.start_simulation.assert_called_once()
        
        # Test pause
        response = self.client.post('/api/simulation/pause')
        assert response.status_code == 200
        mock_simulator.pause_simulation.assert_called_once()
        
        # Test stop
        response = self.client.post('/api/simulation/stop')
        assert response.status_code == 200
        mock_simulator.stop_simulation.assert_called_once()
    
    def test_incident_report_valid(self):
        """Test valid incident reporting"""
        incident_data = {
            "incident": {
                "id": "INC001",
                "type": "Signal Failure",
                "location": {
                    "latitude": 28.6139,
                    "longitude": 77.2090
                },
                "severity": "high",
                "trainNumber": "12951"
            }
        }
        
        with patch('backend.app.find_trains_near_location') as mock_find_trains, \
             patch('backend.app.simulator') as mock_simulator, \
             patch('backend.app.optimizer') as mock_optimizer:
            
            mock_find_trains.return_value = ["12951"]
            mock_simulator.traffic_state.trains = {
                "12951": Train("12951", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
            }
            mock_optimizer.optimize_traffic.return_value = {
                'milp_solution': {'status': 'Optimal'},
                'applied_changes': {'12951': {'delay_change': 15.0}}
            }
            
            response = self.client.post('/api/incidents/report',
                                      data=json.dumps(incident_data),
                                      content_type='application/json')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
            assert data['optimizationTriggered'] == True
            assert "12951" in data['affectedTrains']
    
    def test_incident_report_invalid(self):
        """Test invalid incident reporting"""
        invalid_data = {
            "incident": {
                "id": "INC001"
                # Missing required fields
            }
        }
        
        response = self.client.post('/api/incidents/report',
                                  data=json.dumps(invalid_data),
                                  content_type='application/json')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    @patch('backend.app.simulator')
    def test_export_data(self, mock_simulator):
        """Test data export endpoint"""
        mock_simulator.export_simulation_data.return_value = {
            'trains': {},
            'performance_data': {'throughput': [1, 2, 3]},
            'network_info': {'stations': 21, 'tracks': 24}
        }
        
        response = self.client.get('/api/export')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'trains' in data
        assert 'performance_data' in data
        assert 'network_info' in data
    
    def test_network_info(self):
        """Test network information endpoint"""
        response = self.client.get('/api/network')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert 'stations' in data
        assert 'tracks' in data
        assert isinstance(data['stations'], list)
        assert isinstance(data['tracks'], list)
    
    def test_error_handling(self):
        """Test API error handling"""
        # Test invalid JSON
        response = self.client.post('/api/trains',
                                  data='invalid json',
                                  content_type='application/json')
        assert response.status_code == 400
        
        # Test non-existent endpoint
        response = self.client.get('/api/nonexistent')
        assert response.status_code == 404


class TestWebSocketAPI:
    """Test WebSocket functionality"""
    
    def setup_method(self):
        """Setup WebSocket test fixtures"""
        app.config['TESTING'] = True
        self.client = socketio.test_client(app, namespace='/simulation')
    
    def test_websocket_connection(self):
        """Test WebSocket connection"""
        assert self.client.is_connected(namespace='/simulation')
    
    def test_websocket_events(self):
        """Test WebSocket event handling"""
        # Test connect event
        received = self.client.get_received(namespace='/simulation')
        
        # Should receive connected event
        connect_events = [event for event in received if event['name'] == 'connected']
        assert len(connect_events) > 0
        assert connect_events[0]['args'][0]['status'] == 'online'
    
    def test_optimization_request(self):
        """Test optimization request via WebSocket"""
        with patch('backend.app.optimizer') as mock_optimizer:
            mock_optimizer.optimize_traffic.return_value = {
                'rl_action': 3,
                'milp_solution': {'status': 'Optimal'},
                'optimization_time': 2.1
            }
            
            # Send optimization request
            self.client.emit('request_optimization', namespace='/simulation')
            
            # Check for response
            received = self.client.get_received(namespace='/simulation')
            optimization_events = [event for event in received if event['name'] == 'optimization_result']
            
            assert len(optimization_events) > 0
            result = optimization_events[0]['args'][0]
            assert result['success'] == True
            assert 'result' in result
    
    def test_add_train_websocket(self):
        """Test adding train via WebSocket"""
        train_data = {
            'id': 'WS001',
            'route': ['DEL', 'GZB'],
            'priority': 'EXPRESS',
            'scheduled_times': [0, 0.5],
            'delay': 0.0
        }
        
        self.client.emit('add_train', train_data, namespace='/simulation')
        
        # Check for response
        received = self.client.get_received(namespace='/simulation')
        train_events = [event for event in received if event['name'] == 'train_added']
        
        assert len(train_events) > 0
        result = train_events[0]['args'][0]
        assert result['success'] == True
    
    def test_websocket_error_handling(self):
        """Test WebSocket error handling"""
        # Send invalid train data
        invalid_data = {
            'id': 'INVALID'
            # Missing required fields
        }
        
        self.client.emit('add_train', invalid_data, namespace='/simulation')
        
        # Check for error response
        received = self.client.get_received(namespace='/simulation')
        error_events = [event for event in received if event['name'] == 'error']
        
        assert len(error_events) > 0
        error = error_events[0]['args'][0]
        assert 'message' in error


class TestAPIIntegration:
    """Integration tests for API with full system"""
    
    def setup_method(self):
        """Setup integration test fixtures"""
        app.config['TESTING'] = True
        self.client = app.test_client()
        self.ctx = app.app_context()
        self.ctx.push()
    
    def teardown_method(self):
        """Cleanup after tests"""
        self.ctx.pop()
    
    def test_full_workflow(self):
        """Test complete API workflow"""
        # 1. Add trains
        trains = [
            {
                "id": "API001",
                "route": ["DEL", "GZB", "ALD"],
                "priority": "EXPRESS",
                "scheduled_times": [0, 0.5, 10.5],
                "delay": 5.0
            },
            {
                "id": "API002",
                "route": ["BPL", "NGP"],
                "priority": "FREIGHT",
                "scheduled_times": [0, 10.0],
                "delay": 15.0
            }
        ]
        
        for train_data in trains:
            response = self.client.post('/api/trains',
                                      data=json.dumps(train_data),
                                      content_type='application/json')
            assert response.status_code == 200
        
        # 2. Get trains
        response = self.client.get('/api/trains')
        assert response.status_code == 200
        data = json.loads(response.data)
        assert len(data) == 2
        assert "API001" in data
        assert "API002" in data
        
        # 3. Run optimization
        with patch('backend.app.optimizer') as mock_optimizer:
            mock_optimizer.optimize_traffic.return_value = {
                'rl_action': 7,
                'milp_solution': {'status': 'Optimal'},
                'optimization_time': 1.8,
                'recommendations': ['Optimize route for API001']
            }
            
            response = self.client.post('/api/optimization/run')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
        
        # 4. Get metrics
        with patch('backend.app.simulator') as mock_simulator:
            mock_simulator._get_current_metrics.return_value = {
                'throughput': 2,
                'average_delay': 10.0,
                'capacity_utilization': 0.3,
                'active_trains': 2
            }
            
            response = self.client.get('/api/metrics')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['throughput'] == 2
            assert data['active_trains'] == 2
        
        # 5. Report incident
        incident_data = {
            "incident": {
                "id": "API_INC001",
                "type": "Track Fault",
                "location": {"latitude": 28.6, "longitude": 77.2},
                "severity": "medium"
            }
        }
        
        with patch('backend.app.find_trains_near_location') as mock_find, \
             patch('backend.app.simulator') as mock_sim, \
             patch('backend.app.optimizer') as mock_opt:
            
            mock_find.return_value = ["API001"]
            mock_sim.traffic_state.trains = {
                "API001": Train("API001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
            }
            mock_opt.optimize_traffic.return_value = {
                'milp_solution': {'status': 'Optimal'}
            }
            
            response = self.client.post('/api/incidents/report',
                                      data=json.dumps(incident_data),
                                      content_type='application/json')
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['success'] == True
    
    def test_concurrent_requests(self):
        """Test handling concurrent API requests"""
        import threading
        import time
        
        results = []
        
        def make_request():
            response = self.client.get('/api/network')
            results.append(response.status_code)
        
        # Create multiple threads
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # All requests should succeed
        assert len(results) == 10
        assert all(status == 200 for status in results)
    
    def test_api_performance(self):
        """Test API response times"""
        import time
        
        # Test network endpoint performance
        start_time = time.time()
        response = self.client.get('/api/network')
        end_time = time.time()
        
        assert response.status_code == 200
        assert (end_time - start_time) < 1.0  # Should respond within 1 second
        
        # Test metrics endpoint performance
        with patch('backend.app.simulator') as mock_simulator:
            mock_simulator._get_current_metrics.return_value = {
                'throughput': 0, 'average_delay': 0,
                'capacity_utilization': 0, 'active_trains': 0
            }
            
            start_time = time.time()
            response = self.client.get('/api/metrics')
            end_time = time.time()
            
            assert response.status_code == 200
            assert (end_time - start_time) < 0.5  # Should be very fast


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
