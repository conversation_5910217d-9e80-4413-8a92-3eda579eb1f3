#!/usr/bin/env python3
"""
Comprehensive Test Runner for AI-Powered Train Traffic Control System
Runs all tests with detailed reporting and coverage analysis
"""

import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(command, description):
    """Run a command and return success status"""
    print(f"\n{'='*60}")
    print(f"🔄 {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=project_root)
        end_time = time.time()
        
        print(f"⏱️  Execution time: {end_time - start_time:.2f} seconds")
        
        if result.returncode == 0:
            print(f"✅ {description} - PASSED")
            if result.stdout:
                print("\n📋 Output:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} - FAILED")
            if result.stderr:
                print("\n🚨 Error Output:")
                print(result.stderr)
            if result.stdout:
                print("\n📋 Standard Output:")
                print(result.stdout)
            return False
            
    except Exception as e:
        end_time = time.time()
        print(f"⏱️  Execution time: {end_time - start_time:.2f} seconds")
        print(f"💥 {description} - ERROR: {str(e)}")
        return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pytest',
        'pytest-cov',
        'pytest-html',
        'torch',
        'flask',
        'flask-socketio',
        'networkx',
        'numpy',
        'pulp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("📦 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All dependencies are installed")
    return True

def run_unit_tests(coverage=True, html_report=True):
    """Run unit tests with optional coverage and HTML reporting"""
    test_files = [
        "tests/test_ai_optimizer.py",
        "tests/test_train_simulator.py",
        "tests/test_api.py"
    ]
    
    # Base pytest command
    cmd_parts = ["python", "-m", "pytest"]
    
    # Add test files
    cmd_parts.extend(test_files)
    
    # Add verbose output
    cmd_parts.extend(["-v", "--tb=short"])
    
    # Add coverage if requested
    if coverage:
        cmd_parts.extend([
            "--cov=models",
            "--cov=simulation", 
            "--cov=backend",
            "--cov-report=term-missing",
            "--cov-report=xml:coverage.xml"
        ])
        
        if html_report:
            cmd_parts.append("--cov-report=html:htmlcov")
    
    # Add HTML test report
    if html_report:
        cmd_parts.append("--html=test_report.html")
        cmd_parts.append("--self-contained-html")
    
    command = " ".join(cmd_parts)
    return run_command(command, "Unit Tests")

def run_integration_tests():
    """Run integration tests"""
    command = "python -m pytest tests/ -k 'integration or Integration' -v"
    return run_command(command, "Integration Tests")

def run_performance_tests():
    """Run performance benchmarks"""
    command = "python -m pytest tests/ -k 'performance or benchmark' -v --durations=10"
    return run_command(command, "Performance Tests")

def run_linting():
    """Run code linting"""
    commands = [
        ("python -m flake8 models/ simulation/ backend/ --max-line-length=120 --ignore=E501,W503", "Flake8 Linting"),
        ("python -m pylint models/ simulation/ backend/ --disable=C0103,R0903,R0913", "Pylint Analysis")
    ]
    
    results = []
    for command, description in commands:
        try:
            results.append(run_command(command, description))
        except:
            print(f"⚠️  {description} not available (package not installed)")
            results.append(True)  # Don't fail if linting tools aren't installed
    
    return all(results)

def run_security_checks():
    """Run security vulnerability checks"""
    commands = [
        ("python -m safety check", "Safety - Dependency Vulnerability Check"),
        ("python -m bandit -r models/ simulation/ backend/", "Bandit - Security Issues Check")
    ]
    
    results = []
    for command, description in commands:
        try:
            results.append(run_command(command, description))
        except:
            print(f"⚠️  {description} not available (package not installed)")
            results.append(True)  # Don't fail if security tools aren't installed
    
    return all(results)

def generate_test_summary(results):
    """Generate a comprehensive test summary"""
    print(f"\n{'='*80}")
    print("📊 TEST EXECUTION SUMMARY")
    print(f"{'='*80}")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📈 Total Test Suites: {total_tests}")
    print(f"✅ Passed: {passed_tests}")
    print(f"❌ Failed: {failed_tests}")
    print(f"📊 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    if failed_tests > 0:
        print(f"\n🚨 {failed_tests} test suite(s) failed. Please review the output above.")
        return False
    else:
        print(f"\n🎉 All test suites passed successfully!")
        return True

def main():
    """Main test runner function"""
    parser = argparse.ArgumentParser(description="AI Traffic Control System Test Runner")
    parser.add_argument("--no-coverage", action="store_true", help="Skip coverage analysis")
    parser.add_argument("--no-html", action="store_true", help="Skip HTML report generation")
    parser.add_argument("--unit-only", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration-only", action="store_true", help="Run only integration tests")
    parser.add_argument("--performance-only", action="store_true", help="Run only performance tests")
    parser.add_argument("--lint-only", action="store_true", help="Run only linting")
    parser.add_argument("--security-only", action="store_true", help="Run only security checks")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only (unit tests without coverage)")
    
    args = parser.parse_args()
    
    print("🚂 AI-Powered Train Traffic Control System - Test Suite")
    print("=" * 80)
    
    # Check dependencies first
    if not check_dependencies():
        print("❌ Dependency check failed. Please install missing packages.")
        return 1
    
    # Determine which tests to run
    results = {}
    
    if args.quick:
        results["Unit Tests (Quick)"] = run_unit_tests(coverage=False, html_report=False)
    elif args.unit_only:
        results["Unit Tests"] = run_unit_tests(coverage=not args.no_coverage, html_report=not args.no_html)
    elif args.integration_only:
        results["Integration Tests"] = run_integration_tests()
    elif args.performance_only:
        results["Performance Tests"] = run_performance_tests()
    elif args.lint_only:
        results["Code Linting"] = run_linting()
    elif args.security_only:
        results["Security Checks"] = run_security_checks()
    else:
        # Run all tests
        results["Unit Tests"] = run_unit_tests(coverage=not args.no_coverage, html_report=not args.no_html)
        results["Integration Tests"] = run_integration_tests()
        results["Performance Tests"] = run_performance_tests()
        results["Code Linting"] = run_linting()
        results["Security Checks"] = run_security_checks()
    
    # Generate summary
    success = generate_test_summary(results)
    
    # Print additional information
    if not args.no_html and not args.quick:
        print(f"\n📄 Reports Generated:")
        if os.path.exists("test_report.html"):
            print(f"   📋 Test Report: test_report.html")
        if os.path.exists("htmlcov/index.html"):
            print(f"   📊 Coverage Report: htmlcov/index.html")
        if os.path.exists("coverage.xml"):
            print(f"   📈 Coverage XML: coverage.xml")
    
    print(f"\n🏁 Test execution completed.")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
