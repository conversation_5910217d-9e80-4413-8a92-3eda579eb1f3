{"name": "ai-train-traffic-control", "version": "1.0.0", "description": "AI-Powered Precise Train Traffic Control System for Indian Railways", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "node server.js"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "socket.io-client": "^4.7.2", "axios": "^1.5.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "lucide-react": "^0.279.0", "@headlessui/react": "^1.7.17", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5"}, "keywords": ["railway", "ai", "traffic-control", "optimization", "indian-railways"], "author": "Team SHHHI", "license": "MIT"}