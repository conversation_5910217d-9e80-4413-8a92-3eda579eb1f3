"""
Comprehensive Test Suite for AI Optimizer
Tests the hybrid RL-MILP optimization system
"""

import pytest
import numpy as np
import torch
from unittest.mock import Mock, patch
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.ai_optimizer import HybridOptimizer, DQNAgent, MILPOptimizer, Train, TrafficState, TrainStatus, TrainPriority
from models.railway_network import create_sample_network


class TestDQNAgent:
    """Test the Deep Q-Network component"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.state_size = 108
        self.action_size = 10
        self.dqn = DQNAgent(self.state_size, self.action_size)
    
    def test_dqn_initialization(self):
        """Test DQN network initialization"""
        assert self.dqn.fc1.in_features == self.state_size
        assert self.dqn.fc4.out_features == self.action_size
        assert isinstance(self.dqn.dropout, torch.nn.Dropout)
    
    def test_dqn_forward_pass(self):
        """Test forward pass through DQN"""
        state = torch.randn(1, self.state_size)
        output = self.dqn(state)
        
        assert output.shape == (1, self.action_size)
        assert not torch.isnan(output).any()
        assert not torch.isinf(output).any()
    
    def test_dqn_batch_processing(self):
        """Test DQN with batch input"""
        batch_size = 32
        state_batch = torch.randn(batch_size, self.state_size)
        output = self.dqn(state_batch)
        
        assert output.shape == (batch_size, self.action_size)
        assert not torch.isnan(output).any()


class TestMILPOptimizer:
    """Test the MILP optimization component"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.network = create_sample_network()
        self.milp_optimizer = MILPOptimizer(self.network)
    
    def test_milp_initialization(self):
        """Test MILP optimizer initialization"""
        assert self.milp_optimizer.network == self.network
    
    def test_empty_train_optimization(self):
        """Test optimization with no trains"""
        result = self.milp_optimizer.optimize_schedule([])
        
        assert result['status'] == 'Optimal'
        assert result['objective'] == 0.0
        assert result['schedule'] == {}
    
    def test_single_train_optimization(self):
        """Test optimization with single train"""
        train = Train(
            id="TEST001",
            route=["DEL", "GZB", "ALD"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5, 10.5],
            current_position=0,
            current_time=0.0,
            delay=5.0
        )
        
        result = self.milp_optimizer.optimize_schedule([train])
        
        assert result['status'] == 'Optimal'
        assert 'schedule' in result
        assert 'TEST001' in result['schedule']
        assert 'optimized_delay' in result['schedule']['TEST001']
        assert result['schedule']['TEST001']['optimized_delay'] <= train.delay
    
    def test_multiple_train_optimization(self):
        """Test optimization with multiple trains"""
        trains = [
            Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=10.0),
            Train("T002", ["GZB", "ALD"], TrainPriority.PASSENGER, [0, 10.0], delay=15.0),
            Train("T003", ["ALD", "JHS"], TrainPriority.FREIGHT, [0, 5.0], delay=20.0)
        ]
        
        result = self.milp_optimizer.optimize_schedule(trains)
        
        assert result['status'] == 'Optimal'
        assert len(result['schedule']) == 3
        assert 'recommendations' in result
        assert isinstance(result['recommendations'], list)
    
    def test_optimization_with_conflicts(self):
        """Test optimization with track conflicts"""
        trains = [
            Train("T001", ["DEL", "GZB", "ALD"], TrainPriority.EXPRESS, [0, 0.5, 10.5], delay=0.0, status=TrainStatus.RUNNING),
            Train("T002", ["DEL", "GZB", "ALD"], TrainPriority.PASSENGER, [0.1, 0.6, 10.6], delay=0.0, status=TrainStatus.RUNNING)
        ]
        
        result = self.milp_optimizer.optimize_schedule(trains)
        
        assert result['status'] == 'Optimal'
        # Should detect conflict and provide recommendations
        recommendations = result['recommendations']
        conflict_detected = any('conflict' in rec.lower() for rec in recommendations)
        assert conflict_detected or len(recommendations) > 0


class TestTrafficState:
    """Test the traffic state management"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.network = create_sample_network()
        self.traffic_state = TrafficState(self.network)
    
    def test_traffic_state_initialization(self):
        """Test traffic state initialization"""
        assert len(self.traffic_state.trains) == 0
        assert self.traffic_state.current_time == 0.0
        assert len(self.traffic_state.track_occupancy) == len(self.network.tracks)
        assert len(self.traffic_state.station_occupancy) == len(self.network.stations)
    
    def test_add_train(self):
        """Test adding train to traffic state"""
        train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        self.traffic_state.add_train(train)
        
        assert "TEST001" in self.traffic_state.trains
        assert self.traffic_state.trains["TEST001"] == train
    
    def test_state_vector_generation(self):
        """Test state vector generation for RL"""
        # Add some trains
        trains = [
            Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=5.0),
            Train("T002", ["ALD", "JHS"], TrainPriority.PASSENGER, [0, 10.0], delay=10.0)
        ]
        
        for train in trains:
            self.traffic_state.add_train(train)
        
        state_vector = self.traffic_state.get_state_vector()
        
        # Check state vector properties
        expected_size = len(self.network.stations) * 4 + len(self.network.tracks)
        assert state_vector.shape == (expected_size,)
        assert not np.isnan(state_vector).any()
        assert not np.isinf(state_vector).any()
        assert np.all(state_vector >= 0)  # All values should be non-negative


class TestHybridOptimizer:
    """Test the complete hybrid RL-MILP optimizer"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.network = create_sample_network()
        self.optimizer = HybridOptimizer(self.network)
        self.traffic_state = TrafficState(self.network)
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization"""
        assert self.optimizer.network == self.network
        assert isinstance(self.optimizer.dqn, DQNAgent)
        assert isinstance(self.optimizer.target_dqn, DQNAgent)
        assert isinstance(self.optimizer.milp_optimizer, MILPOptimizer)
        assert self.optimizer.epsilon == 1.0
        assert len(self.optimizer.memory) == 0
    
    def test_action_selection(self):
        """Test RL action selection"""
        state = np.random.rand(self.optimizer.state_size)
        
        # Test with epsilon (random action)
        action_random = self.optimizer.get_action(state, use_epsilon=True)
        assert 0 <= action_random < self.optimizer.action_size
        
        # Test without epsilon (greedy action)
        action_greedy = self.optimizer.get_action(state, use_epsilon=False)
        assert 0 <= action_greedy < self.optimizer.action_size
    
    def test_experience_replay(self):
        """Test experience replay mechanism"""
        # Add some experiences
        for i in range(50):
            state = np.random.rand(self.optimizer.state_size)
            action = np.random.randint(0, self.optimizer.action_size)
            reward = np.random.rand()
            next_state = np.random.rand(self.optimizer.state_size)
            done = False
            
            self.optimizer.remember(state, action, reward, next_state, done)
        
        assert len(self.optimizer.memory) == 50
        
        # Test replay training
        initial_epsilon = self.optimizer.epsilon
        self.optimizer.replay(batch_size=32)
        
        # Epsilon should decay after training
        assert self.optimizer.epsilon <= initial_epsilon
    
    def test_complete_optimization_flow(self):
        """Test complete optimization workflow"""
        # Add test trains
        trains = [
            Train("12951", ["DEL", "GZB", "ALD"], TrainPriority.EXPRESS, [0, 0.5, 10.5], delay=8.0),
            Train("19037", ["DEL", "AGC", "GWL"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5], delay=12.0),
            Train("50001", ["BPL", "NGP"], TrainPriority.FREIGHT, [0, 10.0], delay=25.0)
        ]
        
        for train in trains:
            self.traffic_state.add_train(train)
        
        # Run optimization
        result = self.optimizer.optimize_traffic(self.traffic_state)
        
        # Verify result structure
        assert 'rl_action' in result
        assert 'milp_solution' in result
        assert 'optimization_time' in result
        assert 'recommendations' in result
        assert 'metrics' in result
        
        # Verify RL action
        assert 0 <= result['rl_action'] < self.optimizer.action_size
        
        # Verify MILP solution
        milp_solution = result['milp_solution']
        assert milp_solution['status'] == 'Optimal'
        assert 'schedule' in milp_solution
        
        # Verify optimization time is reasonable
        assert 0 < result['optimization_time'] < 10.0  # Should complete within 10 seconds
        
        # Verify metrics update
        assert result['metrics']['decisions_made'] > 0
        assert result['metrics']['optimization_time'] > 0
    
    def test_reward_calculation(self):
        """Test reward calculation for different scenarios"""
        # Scenario 1: Successful optimization
        trains = [Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=10.0)]
        for train in trains:
            self.traffic_state.add_train(train)
        
        solution = {
            'status': 'Optimal',
            'metrics': {
                'total_delay_before': 10.0,
                'total_delay_after': 5.0,
                'improvement': 5.0
            }
        }
        
        reward = self.optimizer.calculate_reward(self.traffic_state, solution)
        assert reward > 0  # Should be positive for improvement
        
        # Scenario 2: Failed optimization
        failed_solution = {'status': 'Infeasible'}
        reward_failed = self.optimizer.calculate_reward(self.traffic_state, failed_solution)
        assert reward_failed < 0  # Should be negative for failure
    
    def test_target_network_update(self):
        """Test target network update mechanism"""
        # Get initial target network weights
        initial_target_weights = list(self.optimizer.target_dqn.parameters())[0].clone()
        
        # Modify main network weights
        with torch.no_grad():
            list(self.optimizer.dqn.parameters())[0].fill_(1.0)
        
        # Update target network
        self.optimizer.update_target_network()
        
        # Check that target network weights changed
        updated_target_weights = list(self.optimizer.target_dqn.parameters())[0]
        assert not torch.equal(initial_target_weights, updated_target_weights)
    
    def test_model_save_load(self):
        """Test model saving and loading"""
        import tempfile
        
        # Train the model a bit
        state = np.random.rand(self.optimizer.state_size)
        action = self.optimizer.get_action(state)
        reward = 10.0
        next_state = np.random.rand(self.optimizer.state_size)
        
        self.optimizer.remember(state, action, reward, next_state, False)
        
        # Save model
        with tempfile.NamedTemporaryFile(suffix='.pth', delete=False) as f:
            self.optimizer.save_model(f.name)
            
            # Create new optimizer and load model
            new_optimizer = HybridOptimizer(self.network)
            new_optimizer.load_model(f.name)
            
            # Verify loaded model has same epsilon and metrics
            assert new_optimizer.epsilon == self.optimizer.epsilon
            assert new_optimizer.metrics == self.optimizer.metrics
            
            # Cleanup
            os.unlink(f.name)


class TestIntegration:
    """Integration tests for the complete system"""
    
    def setup_method(self):
        """Setup integration test fixtures"""
        self.network = create_sample_network()
        self.optimizer = HybridOptimizer(self.network)
        self.traffic_state = TrafficState(self.network)
    
    def test_realistic_scenario(self):
        """Test with realistic train scenario"""
        # Create realistic train scenario
        trains = [
            Train("12951", ["DEL", "GZB", "ALD", "JHS", "BPL"], TrainPriority.EXPRESS, 
                  [0, 0.5, 10.5, 14.5, 19.5], delay=5.0, status=TrainStatus.RUNNING),
            Train("12952", ["CSTM", "KYN", "NK", "MMR", "BSL"], TrainPriority.EXPRESS,
                  [0, 1.0, 3.0, 5.5, 8.5], delay=0.0, status=TrainStatus.RUNNING),
            Train("19037", ["DEL", "AGC", "GWL", "JHS"], TrainPriority.PASSENGER,
                  [1.0, 4.5, 6.5, 8.5], delay=12.0, status=TrainStatus.DELAYED),
            Train("50001", ["BPL", "NGP", "BSL"], TrainPriority.FREIGHT,
                  [0, 10.0, 14.0], delay=30.0, status=TrainStatus.DELAYED)
        ]
        
        for train in trains:
            self.traffic_state.add_train(train)
        
        # Run multiple optimization cycles
        results = []
        for cycle in range(5):
            result = self.optimizer.optimize_traffic(self.traffic_state)
            results.append(result)
            
            # Verify each result
            assert result['milp_solution']['status'] == 'Optimal'
            assert result['optimization_time'] < 5.0
            assert len(result['recommendations']) >= 0
        
        # Verify learning progression
        assert len(self.optimizer.memory) > 0
        assert self.optimizer.metrics['decisions_made'] == 5
    
    def test_performance_benchmarks(self):
        """Test performance benchmarks"""
        import time
        
        # Create large scenario
        trains = []
        for i in range(20):  # 20 trains
            train = Train(
                f"T{i:03d}",
                ["DEL", "GZB", "ALD", "JHS"],
                TrainPriority.PASSENGER,
                [0, 0.5, 10.5, 14.5],
                delay=float(i % 10)
            )
            trains.append(train)
            self.traffic_state.add_train(train)
        
        # Benchmark optimization time
        start_time = time.time()
        result = self.optimizer.optimize_traffic(self.traffic_state)
        optimization_time = time.time() - start_time
        
        # Performance assertions
        assert optimization_time < 10.0  # Should complete within 10 seconds
        assert result['milp_solution']['status'] == 'Optimal'
        assert len(result['milp_solution']['schedule']) == 20
    
    def test_edge_cases(self):
        """Test edge cases and error handling"""
        # Test with no trains
        result_empty = self.optimizer.optimize_traffic(self.traffic_state)
        assert result_empty['milp_solution']['status'] == 'Optimal'
        
        # Test with train having invalid route
        invalid_train = Train("INVALID", ["NONEXISTENT"], TrainPriority.EXPRESS, [0])
        self.traffic_state.add_train(invalid_train)
        
        # Should handle gracefully
        result_invalid = self.optimizer.optimize_traffic(self.traffic_state)
        assert 'error' not in result_invalid or result_invalid['milp_solution']['status'] in ['Optimal', 'Suboptimal']


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
