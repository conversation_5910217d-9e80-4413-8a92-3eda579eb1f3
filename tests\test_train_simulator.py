"""
Comprehensive Test Suite for Train Simulator
Tests the physics-based train movement and simulation engine
"""

import pytest
import time
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from simulation.train_simulator import TrainSimulator, SimulationConfig, SimulationMode
from models.railway_network import create_sample_network
from models.ai_optimizer import Train, TrainStatus, TrainPriority, TrafficState


class TestSimulationConfig:
    """Test simulation configuration"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = SimulationConfig()
        
        assert config.mode == SimulationMode.REAL_TIME
        assert config.speed_multiplier == 1.0
        assert config.auto_optimize == True
        assert config.show_delays == True
        assert config.show_capacity == True
        assert config.record_metrics == True
    
    def test_custom_config(self):
        """Test custom configuration"""
        config = SimulationConfig(
            mode=SimulationMode.ACCELERATED,
            speed_multiplier=10.0,
            auto_optimize=False
        )
        
        assert config.mode == SimulationMode.ACCELERATED
        assert config.speed_multiplier == 10.0
        assert config.auto_optimize == False


class TestTrainSimulator:
    """Test the main train simulator"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.network = create_sample_network()
        self.config = SimulationConfig(mode=SimulationMode.ACCELERATED, speed_multiplier=10.0)
        self.simulator = TrainSimulator(self.network, self.config)
    
    def test_simulator_initialization(self):
        """Test simulator initialization"""
        assert self.simulator.network == self.network
        assert self.simulator.config == self.config
        assert self.simulator.current_time == 0.0
        assert self.simulator.is_running == False
        assert len(self.simulator.traffic_state.trains) == 0
        assert len(self.simulator.performance_data['throughput']) == 0
    
    def test_add_train(self):
        """Test adding trains to simulation"""
        train = Train(
            id="TEST001",
            route=["DEL", "GZB", "ALD"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5, 10.5],
            current_position=0,
            delay=0.0,
            status=TrainStatus.SCHEDULED
        )
        
        self.simulator.add_train(train)
        
        assert "TEST001" in self.simulator.traffic_state.trains
        assert self.simulator.traffic_state.trains["TEST001"] == train
        assert train.status == TrainStatus.SCHEDULED
    
    def test_train_movement(self):
        """Test basic train movement"""
        train = Train(
            id="TEST001",
            route=["DEL", "GZB"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5],  # 30 minutes travel time
            current_position=0,
            delay=0.0,
            status=TrainStatus.SCHEDULED
        )
        
        self.simulator.add_train(train)
        
        # Step simulation until train moves
        for _ in range(10):
            self.simulator.step_simulation()
            if train.current_position > 0:
                break
        
        # Train should have moved to next station
        assert train.current_position == 1
        assert train.status == TrainStatus.COMPLETED
    
    def test_track_occupancy(self):
        """Test track occupancy management"""
        # Add two trains on same route
        train1 = Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=0.0)
        train2 = Train("T002", ["DEL", "GZB"], TrainPriority.PASSENGER, [0.1, 0.6], delay=0.0)
        
        self.simulator.add_train(train1)
        self.simulator.add_train(train2)
        
        # Step simulation
        for _ in range(20):
            self.simulator.step_simulation()
        
        # Check that track conflicts were handled
        track_id = "DEL-GZB"
        occupancy_history = []
        
        # Verify track was properly managed
        assert track_id in self.simulator.traffic_state.track_occupancy
    
    def test_delay_handling(self):
        """Test train delay handling"""
        train = Train(
            id="DELAYED001",
            route=["DEL", "GZB"],
            priority=TrainPriority.EXPRESS,
            scheduled_times=[0, 0.5],
            delay=10.0,  # 10 minute delay
            status=TrainStatus.DELAYED
        )
        
        self.simulator.add_train(train)
        
        # Train should not move until scheduled time + delay
        initial_position = train.current_position
        
        # Step simulation for less than delay time
        for _ in range(5):
            self.simulator.step_simulation()
        
        # Train should still be at initial position
        assert train.current_position == initial_position
        
        # Step simulation past delay time
        for _ in range(20):
            self.simulator.step_simulation()
        
        # Train should have moved
        assert train.current_position > initial_position
    
    def test_train_priorities(self):
        """Test train priority handling"""
        express_train = Train("EXP001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        freight_train = Train("FRT001", ["DEL", "GZB"], TrainPriority.FREIGHT, [0, 0.5])
        
        self.simulator.add_train(express_train)
        self.simulator.add_train(freight_train)
        
        # Step simulation
        for _ in range(20):
            self.simulator.step_simulation()
        
        # Express train should complete before freight (in case of conflicts)
        if express_train.status == TrainStatus.COMPLETED and freight_train.status == TrainStatus.COMPLETED:
            # Both completed - check completion times or delays
            assert express_train.delay <= freight_train.delay
    
    def test_metrics_calculation(self):
        """Test performance metrics calculation"""
        # Add multiple trains
        trains = [
            Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=5.0),
            Train("T002", ["ALD", "JHS"], TrainPriority.PASSENGER, [0, 10.0], delay=10.0),
            Train("T003", ["BPL", "NGP"], TrainPriority.FREIGHT, [0, 15.0], delay=0.0)
        ]
        
        for train in trains:
            self.simulator.add_train(train)
        
        # Run simulation
        for _ in range(30):
            self.simulator.step_simulation()
        
        metrics = self.simulator._get_current_metrics()
        
        # Verify metrics structure
        assert 'throughput' in metrics
        assert 'average_delay' in metrics
        assert 'capacity_utilization' in metrics
        assert 'active_trains' in metrics
        
        # Verify metrics values
        assert metrics['throughput'] >= 0
        assert metrics['average_delay'] >= 0
        assert 0 <= metrics['capacity_utilization'] <= 1
        assert metrics['active_trains'] >= 0
    
    def test_optimization_integration(self):
        """Test integration with AI optimizer"""
        # Mock optimizer
        mock_optimizer = Mock()
        mock_optimizer.optimize_traffic.return_value = {
            'rl_action': 5,
            'milp_solution': {
                'status': 'Optimal',
                'schedule': {
                    'TEST001': {'optimized_delay': 3.0}
                }
            },
            'applied_changes': {'TEST001': {'delay_change': -2.0}},
            'optimization_time': 1.5
        }
        
        self.simulator.optimizer = mock_optimizer
        
        # Add train
        train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5], delay=5.0)
        self.simulator.add_train(train)
        
        # Enable auto optimization
        self.simulator.config.auto_optimize = True
        
        # Run simulation to trigger optimization
        for _ in range(20):
            self.simulator.step_simulation()
        
        # Verify optimizer was called
        assert mock_optimizer.optimize_traffic.called
        
        # Verify optimization results were applied
        assert train.delay == 3.0  # Should be updated to optimized delay
    
    def test_scenario_generation(self):
        """Test scenario generation"""
        # Test rush hour scenario
        rush_trains = self.simulator._generate_scenario_trains("rush_hour")
        assert len(rush_trains) >= 5
        
        # Verify train properties
        for train in rush_trains:
            assert isinstance(train, Train)
            assert len(train.route) >= 2
            assert len(train.scheduled_times) == len(train.route)
            assert train.priority in [TrainPriority.EXPRESS, TrainPriority.PASSENGER, TrainPriority.FREIGHT]
        
        # Test disruption scenario
        disruption_trains = self.simulator._generate_scenario_trains("disruption")
        assert len(disruption_trains) >= 3
        
        # Should have some delayed trains
        delayed_trains = [t for t in disruption_trains if t.delay > 0]
        assert len(delayed_trains) > 0
    
    def test_simulation_control(self):
        """Test simulation start/stop/pause controls"""
        # Test start
        self.simulator.start_simulation()
        assert self.simulator.is_running == True
        
        # Test pause
        self.simulator.pause_simulation()
        assert self.simulator.is_running == False
        
        # Test resume
        self.simulator.resume_simulation()
        assert self.simulator.is_running == True
        
        # Test stop
        self.simulator.stop_simulation()
        assert self.simulator.is_running == False
        assert self.simulator.current_time == 0.0
    
    def test_event_emission(self):
        """Test event emission during simulation"""
        events_received = []
        
        def mock_event_handler(event):
            events_received.append(event)
        
        # Mock event system
        self.simulator.event_handlers = {'train_moved': [mock_event_handler]}
        
        # Add train and run simulation
        train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        self.simulator.add_train(train)
        
        for _ in range(10):
            self.simulator.step_simulation()
        
        # Should have received events (if train moved)
        if train.current_position > 0:
            assert len(events_received) > 0
    
    def test_performance_data_recording(self):
        """Test performance data recording"""
        # Add trains
        trains = [
            Train("T001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5]),
            Train("T002", ["ALD", "JHS"], TrainPriority.PASSENGER, [0, 10.0])
        ]
        
        for train in trains:
            self.simulator.add_train(train)
        
        # Run simulation
        initial_data_length = len(self.simulator.performance_data['throughput'])
        
        for _ in range(10):
            self.simulator.step_simulation()
        
        # Performance data should be recorded
        final_data_length = len(self.simulator.performance_data['throughput'])
        assert final_data_length > initial_data_length
        
        # Verify data structure
        assert len(self.simulator.performance_data['throughput']) == len(self.simulator.performance_data['average_delay'])
        assert len(self.simulator.performance_data['capacity_utilization']) == len(self.simulator.performance_data['throughput'])
    
    def test_export_data(self):
        """Test data export functionality"""
        # Add some trains and run simulation
        train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        self.simulator.add_train(train)
        
        for _ in range(5):
            self.simulator.step_simulation()
        
        # Export data
        exported_data = self.simulator.export_simulation_data()
        
        # Verify export structure
        assert 'trains' in exported_data
        assert 'performance_data' in exported_data
        assert 'network_info' in exported_data
        assert 'simulation_config' in exported_data
        
        # Verify train data
        assert 'TEST001' in exported_data['trains']
        train_data = exported_data['trains']['TEST001']
        assert 'id' in train_data
        assert 'route' in train_data
        assert 'status' in train_data
        
        # Verify performance data
        perf_data = exported_data['performance_data']
        assert 'throughput' in perf_data
        assert 'average_delay' in perf_data
        assert isinstance(perf_data['throughput'], list)


class TestTrafficState:
    """Test traffic state management in simulation context"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.network = create_sample_network()
        self.traffic_state = TrafficState(self.network)
    
    def test_station_occupancy_tracking(self):
        """Test station occupancy tracking"""
        train = Train("TEST001", ["DEL", "GZB"], TrainPriority.EXPRESS, [0, 0.5])
        self.traffic_state.add_train(train)
        
        # Initially at DEL
        assert "TEST001" in self.traffic_state.station_occupancy["DEL"]
        assert "TEST001" not in self.traffic_state.station_occupancy["GZB"]
        
        # Move train
        train.current_position = 1
        self.traffic_state._update_station_occupancy(train, "DEL", "GZB")
        
        # Should now be at GZB
        assert "TEST001" not in self.traffic_state.station_occupancy["DEL"]
        assert "TEST001" in self.traffic_state.station_occupancy["GZB"]
    
    def test_track_occupancy_tracking(self):
        """Test track occupancy tracking"""
        track_id = "DEL-GZB"
        
        # Initially free
        assert self.traffic_state.track_occupancy[track_id] is None
        
        # Occupy track
        self.traffic_state.occupy_track(track_id, "TEST001")
        assert self.traffic_state.track_occupancy[track_id] == "TEST001"
        
        # Release track
        self.traffic_state.release_track(track_id)
        assert self.traffic_state.track_occupancy[track_id] is None
    
    def test_capacity_calculations(self):
        """Test capacity utilization calculations"""
        # Add trains to fill some capacity
        trains = [
            Train("T001", ["DEL"], TrainPriority.EXPRESS, [0]),
            Train("T002", ["DEL"], TrainPriority.PASSENGER, [0]),
            Train("T003", ["GZB"], TrainPriority.FREIGHT, [0])
        ]
        
        for train in trains:
            self.traffic_state.add_train(train)
        
        # Calculate utilization
        total_capacity = sum(station.capacity for station in self.network.stations.values())
        used_capacity = sum(len(occupants) for occupants in self.traffic_state.station_occupancy.values())
        expected_utilization = used_capacity / total_capacity
        
        state_vector = self.traffic_state.get_state_vector()
        # Capacity utilization should be reflected in state vector
        assert state_vector is not None


class TestIntegrationWithAI:
    """Integration tests between simulator and AI optimizer"""
    
    def setup_method(self):
        """Setup integration test fixtures"""
        self.network = create_sample_network()
        self.config = SimulationConfig(mode=SimulationMode.ACCELERATED, speed_multiplier=5.0)
        self.simulator = TrainSimulator(self.network, self.config)
    
    def test_full_simulation_cycle(self):
        """Test complete simulation cycle with AI optimization"""
        # Add realistic train scenario
        trains = [
            Train("12951", ["DEL", "GZB", "ALD"], TrainPriority.EXPRESS, [0, 0.5, 10.5], delay=5.0),
            Train("19037", ["DEL", "AGC", "GWL"], TrainPriority.PASSENGER, [1.0, 4.5, 6.5], delay=10.0),
            Train("50001", ["BPL", "NGP"], TrainPriority.FREIGHT, [0, 10.0], delay=20.0)
        ]
        
        for train in trains:
            self.simulator.add_train(train)
        
        # Run simulation with optimization
        initial_total_delay = sum(train.delay for train in trains)
        
        for step in range(50):
            self.simulator.step_simulation()
            
            # Check if optimization improved delays
            if step > 10:  # After some optimization cycles
                current_total_delay = sum(train.delay for train in self.simulator.traffic_state.trains.values())
                # Delays should generally improve or stay same
                assert current_total_delay <= initial_total_delay + 5.0  # Allow small variance
        
        # Verify final state
        final_metrics = self.simulator._get_current_metrics()
        assert final_metrics['throughput'] >= 0
        assert final_metrics['average_delay'] >= 0


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
