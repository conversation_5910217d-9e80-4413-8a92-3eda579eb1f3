# Testing Framework Requirements
# Install with: pip install -r tests/requirements-test.txt

# Core Testing Framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-html>=3.2.0
pytest-xdist>=3.3.0  # For parallel test execution
pytest-mock>=3.11.0  # For advanced mocking

# Performance Testing
pytest-benchmark>=4.0.0
pytest-timeout>=2.1.0

# Code Quality and Linting
flake8>=6.0.0
pylint>=2.17.0
black>=23.0.0  # Code formatting
isort>=5.12.0  # Import sorting

# Security Testing
safety>=2.3.0  # Dependency vulnerability scanning
bandit>=1.7.0  # Security issues detection

# Coverage and Reporting
coverage>=7.2.0
coverage-badge>=1.1.0

# Mock and Test Utilities
responses>=0.23.0  # HTTP request mocking
freezegun>=1.2.0   # Time mocking
factory-boy>=3.3.0 # Test data generation

# WebSocket Testing
python-socketio[client]>=5.8.0

# Additional Testing Utilities
parameterized>=0.9.0  # Parameterized tests
hypothesis>=6.82.0    # Property-based testing
tox>=4.6.0           # Testing across environments

# Documentation Testing
doctest>=1.0.0

# Load Testing (Optional)
locust>=2.15.0

# Memory Profiling (Optional)
memory-profiler>=0.60.0
psutil>=5.9.0
