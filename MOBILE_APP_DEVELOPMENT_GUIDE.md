# 📱 Field Operations Mobile App Development Guide

**AI-Powered Train Traffic Control System - Mobile Component**  
**Team SHHHI - Comprehensive Kotlin/Firebase Implementation Guide**

---

## 📋 Executive Summary

This guide provides complete instructions for developing the **Field Operations Mobile App** - a critical component that bridges real-world incidents with our AI traffic control system. The app enables field staff to report incidents instantly, creating a live feedback loop for the AI optimization engine.

### 🎯 App Purpose
- **Primary Users**: Track inspectors, maintenance crew, station masters, field engineers
- **Core Function**: Real-time incident reporting with GPS location and photo evidence
- **Integration**: Direct connection to AI traffic control backend for immediate optimization

---

## 🏗️ Technical Architecture

### **Technology Stack**
- **Language**: <PERSON><PERSON><PERSON> (Android Native)
- **Backend**: Firebase (Firestore, Authentication, Storage, Cloud Functions)
- **Real-time Communication**: Firebase Realtime Database + WebSocket to main system
- **Maps**: Google Maps SDK
- **Camera**: CameraX API
- **Location**: Google Location Services

### **System Integration**
```
Mobile App → Firebase → Cloud Functions → Main AI System → Optimization Engine
     ↓              ↓            ↓              ↓              ↓
GPS Location → Firestore → HTTP API → Traffic State → AI Decision
Photo Data → Storage → Image URL → Incident DB → Route Adjustment
```

---

## 🚀 Development Setup

### **Prerequisites**
1. **Android Studio** (Latest version - Hedgehog or newer)
2. **Firebase Project** with following services enabled:
   - Authentication
   - Firestore Database
   - Cloud Storage
   - Cloud Functions
   - Realtime Database
3. **Google Cloud Project** with Maps API enabled
4. **Physical Android device** for testing (GPS and camera required)

### **Project Initialization**

#### 1. Create New Android Project
```kotlin
// In Android Studio
// File → New → New Project
// Choose "Empty Activity"
// Language: Kotlin
// Minimum SDK: API 24 (Android 7.0)
// Package name: com.shhhi.railwayfield
```

#### 2. Add Dependencies (app/build.gradle)
```kotlin
dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    
    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-functions-ktx'
    
    // Google Services
    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    
    // Camera
    implementation 'androidx.camera:camera-camera2:1.3.1'
    implementation 'androidx.camera:camera-lifecycle:1.3.1'
    implementation 'androidx.camera:camera-view:1.3.1'
    
    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Image Loading
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    
    // Permissions
    implementation 'pub.devrel:easypermissions:3.0.0'
    
    // UI Components
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.7.0'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.7.6'
    implementation 'androidx.navigation:navigation-ui-ktx:2.7.6'
}
```

#### 3. Add Permissions (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

<uses-feature android:name="android.hardware.camera" android:required="true" />
<uses-feature android:name="android.hardware.location.gps" android:required="true" />
```

---

## 📱 Core App Components

### **1. Authentication System**

#### User Authentication (AuthActivity.kt)
```kotlin
class AuthActivity : AppCompatActivity() {
    private lateinit var auth: FirebaseAuth
    private lateinit var binding: ActivityAuthBinding
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAuthBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        auth = Firebase.auth
        
        // Check if user is already logged in
        if (auth.currentUser != null) {
            startMainActivity()
            return
        }
        
        setupAuthUI()
    }
    
    private fun setupAuthUI() {
        binding.btnLogin.setOnClickListener {
            val email = binding.etEmail.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()
            
            if (validateInput(email, password)) {
                loginUser(email, password)
            }
        }
        
        binding.btnRegister.setOnClickListener {
            val email = binding.etEmail.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()
            val employeeId = binding.etEmployeeId.text.toString().trim()
            
            if (validateInput(email, password) && employeeId.isNotEmpty()) {
                registerUser(email, password, employeeId)
            }
        }
    }
    
    private fun loginUser(email: String, password: String) {
        binding.progressBar.visibility = View.VISIBLE
        
        auth.signInWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                binding.progressBar.visibility = View.GONE
                
                if (task.isSuccessful) {
                    startMainActivity()
                } else {
                    showError("Login failed: ${task.exception?.message}")
                }
            }
    }
    
    private fun registerUser(email: String, password: String, employeeId: String) {
        binding.progressBar.visibility = View.VISIBLE
        
        auth.createUserWithEmailAndPassword(email, password)
            .addOnCompleteListener(this) { task ->
                if (task.isSuccessful) {
                    // Create user profile in Firestore
                    createUserProfile(employeeId)
                } else {
                    binding.progressBar.visibility = View.GONE
                    showError("Registration failed: ${task.exception?.message}")
                }
            }
    }
    
    private fun createUserProfile(employeeId: String) {
        val user = auth.currentUser
        val userProfile = hashMapOf(
            "employeeId" to employeeId,
            "email" to user?.email,
            "role" to "field_operator",
            "createdAt" to FieldValue.serverTimestamp(),
            "isActive" to true
        )
        
        Firebase.firestore.collection("users")
            .document(user!!.uid)
            .set(userProfile)
            .addOnSuccessListener {
                binding.progressBar.visibility = View.GONE
                startMainActivity()
            }
            .addOnFailureListener { e ->
                binding.progressBar.visibility = View.GONE
                showError("Profile creation failed: ${e.message}")
            }
    }
    
    private fun validateInput(email: String, password: String): Boolean {
        return when {
            email.isEmpty() -> {
                binding.etEmail.error = "Email required"
                false
            }
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> {
                binding.etEmail.error = "Valid email required"
                false
            }
            password.isEmpty() -> {
                binding.etPassword.error = "Password required"
                false
            }
            password.length < 6 -> {
                binding.etPassword.error = "Password must be at least 6 characters"
                false
            }
            else -> true
        }
    }
    
    private fun startMainActivity() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
```

### **2. Main Dashboard (MainActivity.kt)**
```kotlin
class MainActivity : AppCompatActivity() {
    private lateinit var binding: ActivityMainBinding
    private lateinit var auth: FirebaseAuth
    private lateinit var locationManager: LocationManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        auth = Firebase.auth
        locationManager = LocationManager(this)
        
        setupUI()
        checkPermissions()
    }
    
    private fun setupUI() {
        // Welcome message
        val user = auth.currentUser
        binding.tvWelcome.text = "Welcome, ${user?.email}"
        
        // Big Red Button - Main Feature
        binding.btnReportIncident.setOnClickListener {
            if (hasRequiredPermissions()) {
                startIncidentReporting()
            } else {
                requestPermissions()
            }
        }
        
        // Secondary features
        binding.btnViewHistory.setOnClickListener {
            startActivity(Intent(this, HistoryActivity::class.java))
        }
        
        binding.btnProfile.setOnClickListener {
            startActivity(Intent(this, ProfileActivity::class.java))
        }
        
        binding.btnLogout.setOnClickListener {
            auth.signOut()
            startActivity(Intent(this, AuthActivity::class.java))
            finish()
        }
        
        // Status indicator
        updateConnectionStatus()
    }
    
    private fun startIncidentReporting() {
        val intent = Intent(this, IncidentReportActivity::class.java)
        startActivity(intent)
    }
    
    private fun hasRequiredPermissions(): Boolean {
        return EasyPermissions.hasPermissions(
            this,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.CAMERA
        )
    }
    
    private fun requestPermissions() {
        EasyPermissions.requestPermissions(
            this,
            "This app needs location and camera permissions to report incidents.",
            PERMISSION_REQUEST_CODE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.CAMERA
        )
    }
    
    companion object {
        private const val PERMISSION_REQUEST_CODE = 123
    }
}
```

---

## 🚨 Core Feature: Incident Reporting

### **Incident Report Activity (IncidentReportActivity.kt)**
```kotlin
class IncidentReportActivity : AppCompatActivity() {
    private lateinit var binding: ActivityIncidentReportBinding
    private lateinit var locationManager: LocationManager
    private lateinit var cameraManager: CameraManager
    private var currentLocation: Location? = null
    private var capturedImageUri: Uri? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityIncidentReportBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        locationManager = LocationManager(this)
        cameraManager = CameraManager(this)
        
        setupUI()
        getCurrentLocation()
    }
    
    private fun setupUI() {
        // Incident type dropdown
        val incidentTypes = arrayOf(
            "Signal Failure",
            "Track Fault", 
            "Engine Breakdown",
            "Security Alert",
            "Infrastructure Damage",
            "Weather Related",
            "Other"
        )
        
        val adapter = ArrayAdapter(this, android.R.layout.simple_dropdown_item_1line, incidentTypes)
        binding.spinnerIncidentType.adapter = adapter
        
        // Location button
        binding.btnUseCurrentLocation.setOnClickListener {
            getCurrentLocation()
        }
        
        // Camera button
        binding.btnTakePhoto.setOnClickListener {
            cameraManager.captureImage { uri ->
                capturedImageUri = uri
                binding.ivPreview.setImageURI(uri)
                binding.ivPreview.visibility = View.VISIBLE
            }
        }
        
        // Submit button
        binding.btnSubmit.setOnClickListener {
            submitIncidentReport()
        }
        
        // Cancel button
        binding.btnCancel.setOnClickListener {
            finish()
        }
    }
    
    private fun getCurrentLocation() {
        binding.progressLocation.visibility = View.VISIBLE
        
        locationManager.getCurrentLocation { location ->
            binding.progressLocation.visibility = View.GONE
            
            if (location != null) {
                currentLocation = location
                binding.tvLocation.text = "Location: ${location.latitude}, ${location.longitude}"
                binding.btnUseCurrentLocation.text = "✓ Location Captured"
                binding.btnUseCurrentLocation.isEnabled = false
            } else {
                showError("Failed to get location. Please try again.")
            }
        }
    }
    
    private fun submitIncidentReport() {
        val incidentType = binding.spinnerIncidentType.selectedItem.toString()
        val trainNumber = binding.etTrainNumber.text.toString().trim()
        val description = binding.etDescription.text.toString().trim()
        
        // Validation
        if (incidentType.isEmpty()) {
            showError("Please select incident type")
            return
        }
        
        if (currentLocation == null) {
            showError("Location is required. Please capture location first.")
            return
        }
        
        binding.progressSubmit.visibility = View.VISIBLE
        binding.btnSubmit.isEnabled = false
        
        // Create incident report
        val incident = IncidentReport(
            id = UUID.randomUUID().toString(),
            type = incidentType,
            location = GeoPoint(currentLocation!!.latitude, currentLocation!!.longitude),
            trainNumber = trainNumber.ifEmpty { null },
            description = description.ifEmpty { null },
            reportedBy = Firebase.auth.currentUser?.uid ?: "",
            reportedAt = Timestamp.now(),
            status = "reported",
            imageUrl = null // Will be updated after image upload
        )
        
        // Upload image first (if available), then submit report
        if (capturedImageUri != null) {
            uploadImage(incident)
        } else {
            saveIncidentReport(incident)
        }
    }
    
    private fun uploadImage(incident: IncidentReport) {
        val imageRef = Firebase.storage.reference
            .child("incident_images/${incident.id}.jpg")
        
        imageRef.putFile(capturedImageUri!!)
            .addOnSuccessListener { taskSnapshot ->
                imageRef.downloadUrl.addOnSuccessListener { uri ->
                    incident.imageUrl = uri.toString()
                    saveIncidentReport(incident)
                }
            }
            .addOnFailureListener { e ->
                // Continue without image
                showError("Image upload failed, submitting without image")
                saveIncidentReport(incident)
            }
    }
    
    private fun saveIncidentReport(incident: IncidentReport) {
        Firebase.firestore.collection("incidents")
            .document(incident.id)
            .set(incident)
            .addOnSuccessListener {
                // Notify main system via Cloud Function
                notifyMainSystem(incident)
            }
            .addOnFailureListener { e ->
                binding.progressSubmit.visibility = View.GONE
                binding.btnSubmit.isEnabled = true
                showError("Failed to submit report: ${e.message}")
            }
    }
    
    private fun notifyMainSystem(incident: IncidentReport) {
        val functions = Firebase.functions
        val data = hashMapOf(
            "incidentId" to incident.id,
            "type" to incident.type,
            "location" to mapOf(
                "latitude" to incident.location.latitude,
                "longitude" to incident.location.longitude
            ),
            "trainNumber" to incident.trainNumber,
            "severity" to determineSeverity(incident.type)
        )
        
        functions.getHttpsCallable("notifyTrafficControl")
            .call(data)
            .addOnSuccessListener { result ->
                binding.progressSubmit.visibility = View.GONE
                showSuccess("Incident reported successfully! AI system has been notified.")
                
                // Return to main activity
                Handler(Looper.getMainLooper()).postDelayed({
                    finish()
                }, 2000)
            }
            .addOnFailureListener { e ->
                binding.progressSubmit.visibility = View.GONE
                showError("Report saved but failed to notify AI system: ${e.message}")
                
                // Still finish the activity as the report was saved
                Handler(Looper.getMainLooper()).postDelayed({
                    finish()
                }, 3000)
            }
    }
    
    private fun determineSeverity(incidentType: String): String {
        return when (incidentType) {
            "Signal Failure", "Track Fault", "Engine Breakdown" -> "high"
            "Infrastructure Damage", "Security Alert" -> "medium"
            else -> "low"
        }
    }
    
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
    
    private fun showSuccess(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
```

---

## 🔧 Supporting Classes

### **Data Models**
```kotlin
data class IncidentReport(
    val id: String = "",
    val type: String = "",
    val location: GeoPoint = GeoPoint(0.0, 0.0),
    val trainNumber: String? = null,
    val description: String? = null,
    val reportedBy: String = "",
    val reportedAt: Timestamp = Timestamp.now(),
    val status: String = "reported",
    var imageUrl: String? = null
)

data class UserProfile(
    val employeeId: String = "",
    val email: String = "",
    val role: String = "",
    val isActive: Boolean = true,
    val createdAt: Timestamp = Timestamp.now()
)
```

### **Location Manager**
```kotlin
class LocationManager(private val context: Context) {
    private val fusedLocationClient: FusedLocationProviderClient =
        LocationServices.getFusedLocationProviderClient(context)
    
    fun getCurrentLocation(callback: (Location?) -> Unit) {
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            callback(null)
            return
        }
        
        fusedLocationClient.lastLocation
            .addOnSuccessListener { location: Location? ->
                if (location != null) {
                    callback(location)
                } else {
                    // Request fresh location
                    requestFreshLocation(callback)
                }
            }
            .addOnFailureListener {
                callback(null)
            }
    }
    
    private fun requestFreshLocation(callback: (Location?) -> Unit) {
        val locationRequest = LocationRequest.create().apply {
            priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            interval = 10000
            fastestInterval = 5000
        }
        
        val locationCallback = object : LocationCallback() {
            override fun onLocationResult(locationResult: LocationResult) {
                fusedLocationClient.removeLocationUpdates(this)
                callback(locationResult.lastLocation)
            }
        }
        
        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.ACCESS_FINE_LOCATION
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback,
                Looper.getMainLooper()
            )
        }
    }
}
```

### **Camera Manager**
```kotlin
class CameraManager(private val activity: AppCompatActivity) {
    private var imageCapture: ImageCapture? = null
    private lateinit var cameraExecutor: ExecutorService

    init {
        cameraExecutor = Executors.newSingleThreadExecutor()
    }

    fun captureImage(callback: (Uri) -> Unit) {
        val imageCapture = imageCapture ?: return

        val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
            createImageFile()
        ).build()

        imageCapture.takePicture(
            outputFileOptions,
            ContextCompat.getMainExecutor(activity),
            object : ImageCapture.OnImageSavedCallback {
                override fun onError(exception: ImageCaptureException) {
                    Log.e("CameraManager", "Photo capture failed: ${exception.message}", exception)
                }

                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                    output.savedUri?.let { callback(it) }
                }
            }
        )
    }

    private fun createImageFile(): File {
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val storageDir: File = activity.getExternalFilesDir(Environment.DIRECTORY_PICTURES)!!
        return File.createTempFile("INCIDENT_${timeStamp}_", ".jpg", storageDir)
    }
}
```

---

## 🔥 Firebase Configuration

### **1. Firebase Project Setup**

#### Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Project name: "railway-field-operations"
4. Enable Google Analytics (recommended)
5. Choose or create Analytics account

#### Add Android App
1. Click "Add app" → Android
2. Package name: `com.shhhi.railwayfield`
3. App nickname: "Railway Field Operations"
4. Download `google-services.json`
5. Place in `app/` directory

#### Enable Required Services
```bash
# In Firebase Console, enable:
1. Authentication → Sign-in method → Email/Password
2. Firestore Database → Create database (Start in test mode)
3. Storage → Get started (Start in test mode)
4. Functions → Get started
5. Realtime Database → Create database
```

### **2. Firestore Database Structure**
```javascript
// Collection: incidents
{
  "incident_id": {
    "id": "string",
    "type": "string", // "Signal Failure", "Track Fault", etc.
    "location": {
      "latitude": "number",
      "longitude": "number"
    },
    "trainNumber": "string|null",
    "description": "string|null",
    "reportedBy": "string", // User UID
    "reportedAt": "timestamp",
    "status": "string", // "reported", "acknowledged", "resolved"
    "imageUrl": "string|null",
    "severity": "string", // "low", "medium", "high"
    "aiProcessed": "boolean",
    "aiResponse": {
      "optimizationTriggered": "boolean",
      "affectedTrains": ["string"],
      "estimatedImpact": "string"
    }
  }
}

// Collection: users
{
  "user_uid": {
    "employeeId": "string",
    "email": "string",
    "role": "string", // "field_operator", "supervisor", "admin"
    "isActive": "boolean",
    "createdAt": "timestamp",
    "lastActive": "timestamp",
    "incidentCount": "number"
  }
}
```

### **3. Cloud Functions (Node.js)**

#### Setup Cloud Functions
```bash
npm install -g firebase-tools
firebase login
firebase init functions
cd functions
npm install axios
```

#### Main Cloud Function (functions/index.js)
```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const axios = require('axios');

admin.initializeApp();

// Main function to notify AI traffic control system
exports.notifyTrafficControl = functions.https.onCall(async (data, context) => {
  try {
    // Verify user authentication
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { incidentId, type, location, trainNumber, severity } = data;

    // Validate required fields
    if (!incidentId || !type || !location) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    // Prepare payload for main AI system
    const payload = {
      incident: {
        id: incidentId,
        type: type,
        location: {
          latitude: location.latitude,
          longitude: location.longitude
        },
        trainNumber: trainNumber,
        severity: severity,
        timestamp: admin.firestore.Timestamp.now(),
        source: 'mobile_app'
      }
    };

    // Send to main AI traffic control system
    const AI_SYSTEM_URL = 'http://your-ai-system-url:5000/api/incidents/report';

    const response = await axios.post(AI_SYSTEM_URL, payload, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
      },
      timeout: 10000
    });

    // Update incident with AI response
    await admin.firestore().collection('incidents').doc(incidentId).update({
      aiProcessed: true,
      aiResponse: {
        optimizationTriggered: response.data.optimizationTriggered || false,
        affectedTrains: response.data.affectedTrains || [],
        estimatedImpact: response.data.estimatedImpact || 'unknown',
        processedAt: admin.firestore.Timestamp.now()
      }
    });

    // Log successful notification
    console.log(`Incident ${incidentId} successfully reported to AI system`);

    return {
      success: true,
      message: 'AI system notified successfully',
      aiResponse: response.data
    };

  } catch (error) {
    console.error('Error notifying AI system:', error);

    // Update incident with error status
    if (data.incidentId) {
      await admin.firestore().collection('incidents').doc(data.incidentId).update({
        aiProcessed: false,
        aiError: error.message,
        errorAt: admin.firestore.Timestamp.now()
      });
    }

    throw new functions.https.HttpsError('internal', 'Failed to notify AI system');
  }
});

// Firestore trigger for real-time incident processing
exports.processIncident = functions.firestore
  .document('incidents/{incidentId}')
  .onCreate(async (snap, context) => {
    const incident = snap.data();
    const incidentId = context.params.incidentId;

    try {
      // Determine urgency based on incident type
      const urgentTypes = ['Signal Failure', 'Track Fault', 'Engine Breakdown'];
      const isUrgent = urgentTypes.includes(incident.type);

      // Send real-time notification to connected clients
      await admin.database().ref('live_incidents').child(incidentId).set({
        ...incident,
        isUrgent: isUrgent,
        createdAt: admin.database.ServerValue.TIMESTAMP
      });

      // Auto-expire non-urgent incidents after 1 hour
      if (!isUrgent) {
        setTimeout(async () => {
          await admin.database().ref('live_incidents').child(incidentId).remove();
        }, 3600000); // 1 hour
      }

    } catch (error) {
      console.error('Error processing incident:', error);
    }
  });

// Cleanup function for old incidents
exports.cleanupOldIncidents = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    const cutoff = admin.firestore.Timestamp.fromDate(
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
    );

    const oldIncidents = await admin.firestore()
      .collection('incidents')
      .where('reportedAt', '<', cutoff)
      .where('status', '==', 'resolved')
      .get();

    const batch = admin.firestore().batch();
    oldIncidents.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Cleaned up ${oldIncidents.size} old incidents`);
  });
```

---

## 🔗 Integration with Main AI System

### **Backend API Endpoint (Add to backend/app.py)**
```python
@app.route('/api/incidents/report', methods=['POST'])
def report_incident():
    """Receive incident reports from mobile app"""
    try:
        data = request.get_json()
        incident = data.get('incident', {})

        # Validate incident data
        required_fields = ['id', 'type', 'location']
        for field in required_fields:
            if field not in incident:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Process incident based on type and severity
        incident_type = incident['type']
        location = incident['location']
        severity = incident.get('severity', 'medium')
        train_number = incident.get('trainNumber')

        # Determine impact on current traffic
        affected_trains = []
        optimization_needed = False

        if incident_type in ['Signal Failure', 'Track Fault']:
            # Find trains near the incident location
            affected_trains = find_trains_near_location(
                location['latitude'],
                location['longitude'],
                radius_km=5.0
            )
            optimization_needed = len(affected_trains) > 0

        elif incident_type == 'Engine Breakdown' and train_number:
            # Find specific train
            if train_number in simulator.traffic_state.trains:
                affected_trains = [train_number]
                optimization_needed = True

                # Add delay to the broken train
                train = simulator.traffic_state.trains[train_number]
                train.delay += 30.0  # 30 minute delay for breakdown
                train.status = TrainStatus.DELAYED

        # Trigger AI optimization if needed
        optimization_result = None
        if optimization_needed and optimizer:
            optimization_result = optimizer.optimize_traffic(simulator.traffic_state)

            # Apply emergency routing if severe incident
            if severity == 'high':
                apply_emergency_routing(affected_trains, incident)

        # Log incident in system
        incident_log = {
            'id': incident['id'],
            'type': incident_type,
            'location': location,
            'severity': severity,
            'affected_trains': affected_trains,
            'optimization_triggered': optimization_needed,
            'timestamp': datetime.now().isoformat(),
            'source': 'mobile_app'
        }

        # Store in logs (you might want to use a database)
        with open('logs/incidents.log', 'a') as f:
            f.write(json.dumps(incident_log) + '\n')

        # Broadcast to connected clients
        socketio.emit('incident_reported', {
            'incident': incident_log,
            'optimization_result': optimization_result
        }, namespace='/simulation')

        response = {
            'success': True,
            'optimizationTriggered': optimization_needed,
            'affectedTrains': affected_trains,
            'estimatedImpact': calculate_estimated_impact(incident_type, affected_trains),
            'message': f'Incident processed. {len(affected_trains)} trains affected.'
        }

        return jsonify(response)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def find_trains_near_location(lat, lng, radius_km=5.0):
    """Find trains within radius of incident location"""
    affected_trains = []

    for train_id, train in simulator.traffic_state.trains.items():
        if train.status in [TrainStatus.RUNNING, TrainStatus.DELAYED]:
            # Get train's current station location
            if train.current_position < len(train.route):
                station_id = train.route[train.current_position]
                if station_id in network.stations:
                    station = network.stations[station_id]

                    # Calculate distance using haversine formula
                    distance = calculate_distance(
                        lat, lng,
                        station.latitude, station.longitude
                    )

                    if distance <= radius_km:
                        affected_trains.append(train_id)

    return affected_trains

def calculate_distance(lat1, lng1, lat2, lng2):
    """Calculate distance between two points using haversine formula"""
    from math import radians, cos, sin, asin, sqrt

    # Convert to radians
    lat1, lng1, lat2, lng2 = map(radians, [lat1, lng1, lat2, lng2])

    # Haversine formula
    dlat = lat2 - lat1
    dlng = lng2 - lng1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlng/2)**2
    c = 2 * asin(sqrt(a))
    r = 6371  # Radius of earth in kilometers

    return c * r

def apply_emergency_routing(affected_trains, incident):
    """Apply emergency routing for affected trains"""
    for train_id in affected_trains:
        if train_id in simulator.traffic_state.trains:
            train = simulator.traffic_state.trains[train_id]

            # Find alternative route if possible
            if train.current_position + 1 < len(train.route):
                current_station = train.route[train.current_position]
                destination = train.route[-1]

                alternative_routes = network.get_alternative_routes(
                    current_station, destination, k=3
                )

                if len(alternative_routes) > 1:
                    # Use second best route as alternative
                    train.route = alternative_routes[1]
                    train.delay += 15.0  # Add delay for rerouting

                    print(f"🔄 Train {train_id} rerouted due to incident")

def calculate_estimated_impact(incident_type, affected_trains):
    """Calculate estimated impact of incident"""
    if not affected_trains:
        return "minimal"

    impact_scores = {
        'Signal Failure': 3,
        'Track Fault': 4,
        'Engine Breakdown': 2,
        'Security Alert': 1,
        'Infrastructure Damage': 5,
        'Weather Related': 2,
        'Other': 1
    }

    base_impact = impact_scores.get(incident_type, 1)
    train_factor = min(len(affected_trains), 5)  # Cap at 5 trains

    total_impact = base_impact * train_factor

    if total_impact <= 3:
        return "low"
    elif total_impact <= 8:
        return "medium"
    else:
        return "high"
```

---

## 📱 UI Layouts

### **Main Activity Layout (activity_main.xml)**
```xml
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@color/background_dark">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_train"
            android:tint="@color/primary_blue" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="Railway Field Operations"
            android:textSize="20sp"
            android:textColor="@color/text_primary"
            android:textStyle="bold" />

        <View
            android:id="@+id/statusIndicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:background="@drawable/status_dot_green" />
    </LinearLayout>

    <!-- Welcome Message -->
    <TextView
        android:id="@+id/tvWelcome"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Welcome, Field Operator"
        android:textSize="16sp"
        android:textColor="@color/text_secondary"
        android:layout_marginBottom="32dp" />

    <!-- Big Red Button - Main Feature -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnReportIncident"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:text="🚨 REPORT INCIDENT"
        android:textSize="24sp"
        android:textStyle="bold"
        android:backgroundTint="@color/emergency_red"
        android:textColor="@android:color/white"
        android:layout_marginBottom="24dp"
        app:cornerRadius="16dp"
        app:elevation="8dp" />

    <!-- Secondary Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnViewHistory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="📋 History"
            android:backgroundTint="@color/secondary_blue"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnProfile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="👤 Profile"
            android:backgroundTint="@color/secondary_blue"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton" />
    </LinearLayout>

    <!-- Status Card -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="System Status"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tvSystemStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="🟢 Connected to AI Traffic Control"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Logout Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnLogout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Logout"
        android:textColor="@color/text_secondary"
        style="@style/Widget.MaterialComponents.Button.TextButton" />

</LinearLayout>
```

---

This comprehensive guide provides everything needed to build the mobile app that integrates seamlessly with the AI traffic control system. The app enables real-time incident reporting with immediate AI system notification and optimization.
