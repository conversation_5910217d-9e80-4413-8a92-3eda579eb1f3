/**
 * AI-Powered Train Traffic Control System
 * Modern Frontend with Working Simulation
 */

class TrafficControlApp {
    constructor() {
        this.socket = null;
        this.map = null;
        this.trains = {};
        this.trainMarkers = {};
        this.stationMarkers = {};
        this.isConnected = false;
        this.simulationRunning = false;
        this.simulationInterval = null;
        this.metrics = {
            throughput: 0,
            delay: 0,
            capacity: 0,
            optimization: 0
        };

        this.simulationSpeed = 1.0;
        this.trackStatus = {}; // Track status storage
        this.eventLog = []; // Real-time event log
        this.maxLogEntries = 100; // Maximum log entries to keep

        this.init();
    }

    init() {
        console.log('🚄 Initializing AI Train Traffic Control System...');
        this.initializeSocket();
        this.initializeMap();
        this.setupEventListeners();
        this.loadSampleData();
        this.initializeStatusIndicators();
        this.startMetricsUpdate();
    }

    initializeSocket() {
        this.socket = io('/simulation');
        
        this.socket.on('connect', () => {
            console.log('✅ Connected to server');
            this.isConnected = true;
            this.updateSystemStatus('Connected', true);
            this.showNotification('🚀 Connected to AI Traffic Control System', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Disconnected from server');
            this.isConnected = false;
            this.updateSystemStatus('Disconnected', false);
            this.showNotification('⚠️ Connection lost', 'error');
        });

        this.socket.on('simulation_update', (data) => {
            this.handleSimulationUpdate(data);
            this.logEvent('simulation_update', `Simulation updated: ${Object.keys(data.trains || {}).length} trains active`, 'info');
        });

        this.socket.on('train_event', (event) => {
            this.handleTrainEvent(event);
            this.logEvent('train_event', `Train ${event.train_id}: ${event.type}`, 'info');
        });

        this.socket.on('incident_reported', (data) => {
            this.handleIncidentReport(data);
        });

        this.socket.on('train_rerouted', (event) => {
            this.handleTrainReroute(event);
        });

        this.socket.on('incident_injected', (event) => {
            this.handleIncidentInjection(event);
        });

        this.socket.on('track_blocked', (data) => {
            this.handleTrackBlocked(data);
        });

        this.socket.on('track_unblocked', (data) => {
            this.handleTrackUnblocked(data);
        });

        this.socket.on('incident_reported', (data) => {
            this.handleMobileIncidentReport(data);
        });

        this.socket.on('station_conflict_resolved', (data) => {
            this.handleStationConflict(data);
        });

        this.socket.on('priority_track_assignment', (data) => {
            this.handlePriorityTrackAssignment(data);
        });
    }

    initializeMap() {
        console.log('🗺️ Initializing map...');
        this.map = L.map('map').setView([20.5937, 78.9629], 5);
        
        // Dark theme tiles
        L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
            attribution: '© OpenStreetMap contributors © CARTO',
            subdomains: 'abcd',
            maxZoom: 19
        }).addTo(this.map);

        setTimeout(() => {
            this.loadNetworkData();
        }, 500);
    }

    loadNetworkData() {
        console.log('📡 Loading railway network...');
        
        // Delhi-Mumbai corridor stations
        const stations = [
            { id: 'NDLS', name: 'New Delhi', lat: 28.6448, lng: 77.2097, type: 'terminal' },
            { id: 'GZB', name: 'Ghaziabad', lat: 28.6692, lng: 77.4538, type: 'junction' },
            { id: 'AGC', name: 'Agra Cantt', lat: 27.1767, lng: 78.0081, type: 'major' },
            { id: 'JHS', name: 'Jhansi', lat: 25.4484, lng: 78.5685, type: 'junction' },
            { id: 'BPL', name: 'Bhopal', lat: 23.2599, lng: 77.4126, type: 'major' },
            { id: 'NGP', name: 'Nagpur', lat: 21.1458, lng: 79.0882, type: 'major' },
            { id: 'BSL', name: 'Bhusaval', lat: 21.0444, lng: 75.7849, type: 'junction' },
            { id: 'KYN', name: 'Kalyan', lat: 19.2437, lng: 73.1355, type: 'major' },
            { id: 'CSTM', name: 'Mumbai CST', lat: 18.9398, lng: 72.8355, type: 'terminal' }
        ];

        // Add station markers
        stations.forEach(station => {
            const marker = L.circleMarker([station.lat, station.lng], {
                radius: station.type === 'terminal' ? 8 : 6,
                fillColor: station.type === 'terminal' ? '#ef4444' : '#3b82f6',
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(this.map).bindPopup(`
                <div style="color: #333; font-family: Arial;">
                    <h4 style="margin: 0 0 5px 0;">${station.name}</h4>
                    <p style="margin: 0; font-size: 12px;">Code: ${station.id}</p>
                    <p style="margin: 0; font-size: 12px;">Type: ${station.type}</p>
                </div>
            `);
            
            this.stationMarkers[station.id] = marker;
        });

        // Draw railway lines with track IDs for blocking
        this.trackLines = {};
        this.trackStatus = {}; // Track status storage

        for (let i = 0; i < stations.length - 1; i++) {
            const trackId = `${stations[i].id}-${stations[i + 1].id}`;
            const line = L.polyline([
                [stations[i].lat, stations[i].lng],
                [stations[i + 1].lat, stations[i + 1].lng]
            ], {
                color: '#6366f1',
                weight: 3,
                opacity: 0.7
            }).addTo(this.map);

            // Enhanced popup with disruption controls
            const popupContent = `
                <div style="color: #333; font-family: Arial; min-width: 200px;">
                    <h4 style="margin: 0 0 10px 0;">Track ${trackId}</h4>
                    <p style="margin: 0; font-size: 12px;">From: ${stations[i].name}</p>
                    <p style="margin: 0; font-size: 12px;">To: ${stations[i + 1].name}</p>
                    <p style="margin: 5px 0; font-size: 12px;">
                        Status: <span id="track-status-${trackId}" style="font-weight: bold; color: #10b981;">Active</span>
                    </p>
                    <p style="margin: 5px 0; font-size: 12px;">
                        Occupancy: <span id="track-occupancy-${trackId}" style="font-weight: bold; color: #6366f1;">Available</span>
                    </p>
                    <div style="margin-top: 10px;">
                        <button id="disrupt-btn-${trackId}"
                                onclick="window.app.disruptTrack('${trackId}')"
                                style="background: #ef4444; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-right: 5px;">
                            🚨 Disrupt Track
                        </button>
                        <button id="restore-btn-${trackId}"
                                onclick="window.app.restoreTrack('${trackId}')"
                                style="background: #10b981; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; display: none;">
                            ✅ Restore Track
                        </button>
                    </div>
                </div>
            `;

            line.bindPopup(popupContent);
            this.trackLines[trackId] = line;
            this.trackStatus[trackId] = 'available';
        }

        // Fit map to show all stations
        const group = new L.featureGroup(Object.values(this.stationMarkers));
        this.map.fitBounds(group.getBounds().pad(0.1));
        
        console.log('✅ Railway network loaded');
    }

    loadSampleData() {
        // Sample trains for demonstration
        this.trains = {
            'T001': {
                id: 'T001',
                name: 'Rajdhani Express',
                from: 'NDLS',
                to: 'CSTM',
                status: 'running',
                delay: 0,
                priority: 'EXPRESS',
                position: { lat: 28.6448, lng: 77.2097 },
                progress: 0
            },
            'T002': {
                id: 'T002', 
                name: 'Shatabdi Express',
                from: 'CSTM',
                to: 'NDLS',
                status: 'running',
                delay: 5,
                priority: 'EXPRESS',
                position: { lat: 18.9398, lng: 72.8355 },
                progress: 0
            },
            'T003': {
                id: 'T003',
                name: 'Freight Train',
                from: 'AGC',
                to: 'NGP',
                status: 'delayed',
                delay: 15,
                priority: 'FREIGHT',
                position: { lat: 27.1767, lng: 78.0081 },
                progress: 0
            }
        };

        this.updateTrainDisplay();
        this.addTrainMarkers();
    }

    addTrainMarkers() {
        Object.values(this.trains).forEach(train => {
            this.createTrainMarker(train);
        });
    }

    createTrainMarker(train) {
        // Remove existing marker if it exists
        if (this.trainMarkers[train.id]) {
            this.map.removeLayer(this.trainMarkers[train.id]);
        }

        // Determine train color based on priority and status
        let trainColor = '#6366f1'; // Default blue
        if (train.priority === 'EXPRESS') trainColor = '#10b981'; // Green for express
        else if (train.priority === 'PASSENGER') trainColor = '#3b82f6'; // Blue for passenger
        else if (train.priority === 'FREIGHT') trainColor = '#f59e0b'; // Orange for freight
        else if (train.priority === 'MAINTENANCE') trainColor = '#8b5cf6'; // Purple for maintenance

        // Add status indicator
        let statusIcon = '🚂';
        if (train.status === 'delayed') statusIcon = '⚠️';
        else if (train.status === 'completed') statusIcon = '✅';
        else if (train.status === 'waiting') statusIcon = '⏸️';

        const marker = L.marker([train.position.lat, train.position.lng], {
            icon: L.divIcon({
                className: 'train-marker',
                html: `<div style="
                    background: ${trainColor};
                    color: white;
                    padding: 3px 8px;
                    border-radius: 15px;
                    font-size: 11px;
                    font-weight: bold;
                    border: 2px solid white;
                    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                    text-align: center;
                    min-width: 60px;
                ">${statusIcon} ${train.id}</div>`,
                iconSize: [70, 25],
                iconAnchor: [35, 12]
            })
        }).addTo(this.map);

        // Enhanced popup with real-time info
        const popupContent = `
            <div style="color: #333; font-family: Arial; min-width: 220px;">
                <h4 style="margin: 0 0 8px 0; color: ${trainColor};">${statusIcon} ${train.name || train.id}</h4>
                <div style="font-size: 12px; line-height: 1.4;">
                    <p style="margin: 2px 0;"><strong>ID:</strong> ${train.id}</p>
                    <p style="margin: 2px 0;"><strong>Priority:</strong> ${train.priority}</p>
                    <p style="margin: 2px 0;"><strong>Status:</strong> <span style="color: ${train.status === 'running' ? '#10b981' : train.status === 'delayed' ? '#ef4444' : '#6366f1'};">${train.status.toUpperCase()}</span></p>
                    <p style="margin: 2px 0;"><strong>Current:</strong> ${train.current_station || 'Unknown'}</p>
                    <p style="margin: 2px 0;"><strong>Next:</strong> ${train.next_station || 'Destination'}</p>
                    <p style="margin: 2px 0;"><strong>Delay:</strong> ${train.delay.toFixed(1)} min</p>
                    <p style="margin: 2px 0;"><strong>Progress:</strong> ${Math.round(train.progress || 0)}%</p>
                    ${train.is_moving ? '<p style="margin: 2px 0; color: #10b981;"><strong>🚄 Moving</strong></p>' : '<p style="margin: 2px 0; color: #6366f1;"><strong>🏁 At Station</strong></p>'}
                </div>
            </div>
        `;

        marker.bindPopup(popupContent);
        this.trainMarkers[train.id] = marker;
    }

    setupEventListeners() {
        // Start button
        document.getElementById('startBtn').addEventListener('click', () => {
            this.startSimulation();
        });

        // Pause button  
        document.getElementById('pauseBtn').addEventListener('click', () => {
            this.pauseSimulation();
        });

        // Stop button
        document.getElementById('stopBtn').addEventListener('click', () => {
            this.stopSimulation();
        });

        // Optimize button
        document.getElementById('optimizeBtn').addEventListener('click', () => {
            this.runOptimization();
        });

        // Test incident button
        document.getElementById('testIncidentBtn').addEventListener('click', () => {
            this.testIncident();
        });
    }

    startSimulation() {
        if (this.simulationRunning) return;
        
        console.log('▶️ Starting simulation...');
        this.simulationRunning = true;
        
        // Update UI with enhanced visual feedback
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');

        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Starting...';
        pauseBtn.disabled = false;
        stopBtn.disabled = false;

        // Start simulation loop with speed control
        this.simulationInterval = setInterval(() => {
            this.updateSimulation();
        }, 2000 / (this.simulationSpeed || 1)); // Update every 2 seconds adjusted by speed

        this.showNotification('🚀 Simulation started successfully', 'success');
        this.updateSystemStatus();
        this.logEvent('simulation', 'Simulation started successfully', 'success');
        
        // Make API call to backend
        fetch('/api/simulation/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Backend simulation started');
                    startBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
                } else {
                    console.error('❌ Failed to start backend simulation');
                    this.showNotification('❌ Failed to start backend simulation', 'error');
                    this.stopSimulation();
                }
            })
            .catch(err => {
                console.error('❌ Error starting simulation:', err);
                this.showNotification('❌ Error connecting to backend', 'error');
                startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
                startBtn.disabled = false;
            });
    }

    pauseSimulation() {
        if (!this.simulationRunning) return;

        console.log('⏸️ Pausing simulation...');
        this.simulationRunning = false;

        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }

        // Update UI with enhanced visual feedback
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');

        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> Resume';
        pauseBtn.disabled = true;
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Paused';

        this.showNotification('⏸️ Simulation paused - click Resume to continue', 'warning');
        this.updateSystemStatus();
        this.logEvent('simulation', 'Simulation paused by user', 'warning');

        fetch('/api/simulation/pause', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Backend simulation paused');
                } else {
                    console.error('❌ Failed to pause backend simulation');
                }
            })
            .catch(err => console.log('Backend call failed:', err));
    }

    stopSimulation() {
        console.log('⏹️ Stopping simulation...');
        this.simulationRunning = false;
        
        if (this.simulationInterval) {
            clearInterval(this.simulationInterval);
            this.simulationInterval = null;
        }
        
        // Reset UI with enhanced visual feedback
        const startBtn = document.getElementById('startBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const stopBtn = document.getElementById('stopBtn');

        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> Start';
        pauseBtn.disabled = true;
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        stopBtn.disabled = true;
        stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Stopping...';

        // Reset train positions
        this.loadSampleData();
        this.addTrainMarkers();

        this.showNotification('⏹️ Simulation stopped and reset to initial state', 'info');
        this.updateSystemStatus();
        this.logEvent('simulation', 'Simulation stopped and reset', 'info');
        
        fetch('/api/simulation/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Backend simulation stopped');
                    stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
                } else {
                    console.error('❌ Failed to stop backend simulation');
                }
            })
            .catch(err => {
                console.log('Backend call failed:', err);
                stopBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
            });
    }

    updateSimulation() {
        // Simulate train movement
        Object.values(this.trains).forEach(train => {
            if (train.status === 'running') {
                // Increase progress
                train.progress += Math.random() * 10 + 5; // 5-15% progress per update
                
                if (train.progress >= 100) {
                    train.progress = 100;
                    train.status = 'completed';
                    this.metrics.throughput += 1;
                }
                
                // Simulate position change (simplified)
                const startLat = train.from === 'NDLS' ? 28.6448 : 18.9398;
                const startLng = train.from === 'NDLS' ? 77.2097 : 72.8355;
                const endLat = train.to === 'CSTM' ? 18.9398 : 28.6448;
                const endLng = train.to === 'CSTM' ? 72.8355 : 77.2097;
                
                const progressRatio = train.progress / 100;
                train.position.lat = startLat + (endLat - startLat) * progressRatio;
                train.position.lng = startLng + (endLng - startLng) * progressRatio;
                
                // Update marker position
                if (this.trainMarkers[train.id]) {
                    this.trainMarkers[train.id].setLatLng([train.position.lat, train.position.lng]);
                }
                
                // Random delay changes
                if (Math.random() < 0.1) { // 10% chance
                    train.delay += Math.random() < 0.5 ? 1 : -1;
                    train.delay = Math.max(0, train.delay);
                }
            }
        });
        
        // Update metrics
        this.metrics.delay = Object.values(this.trains).reduce((sum, train) => sum + train.delay, 0) / Object.keys(this.trains).length;
        this.metrics.capacity = Math.min(95, 60 + Math.random() * 30);
        this.metrics.optimization = 2 + Math.random() * 3;
        
        this.updateTrainDisplay();
        this.updateMetricsDisplay();
    }

    updateTrainDisplay() {
        const trainList = document.getElementById('trainList');
        if (!trainList) return;

        const trainItems = Object.values(this.trains).map(train => {
            // Get status color and icon
            let statusColor = '#6366f1';
            let statusIcon = '🚂';
            if (train.status === 'running') {
                statusColor = '#10b981';
                statusIcon = '🚄';
            } else if (train.status === 'delayed') {
                statusColor = '#ef4444';
                statusIcon = '⚠️';
            } else if (train.status === 'completed') {
                statusColor = '#10b981';
                statusIcon = '✅';
            } else if (train.status === 'waiting') {
                statusColor = '#f59e0b';
                statusIcon = '⏸️';
            }

            // Get priority color
            let priorityColor = '#6366f1';
            if (train.priority === 1 || train.priority === 'EXPRESS') priorityColor = '#10b981';
            else if (train.priority === 2 || train.priority === 'PASSENGER') priorityColor = '#3b82f6';
            else if (train.priority === 3 || train.priority === 'FREIGHT') priorityColor = '#f59e0b';
            else if (train.priority === 4 || train.priority === 'MAINTENANCE') priorityColor = '#8b5cf6';

            const priorityName = this.getPriorityName(train.priority) || train.priority;

            return `
                <div class="train-item" style="border-left: 4px solid ${priorityColor}; margin-bottom: 12px; background: rgba(255,255,255,0.05); border-radius: 8px; padding: 12px;">
                    <div class="train-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <div class="train-id" style="font-weight: bold; font-size: 16px; color: ${priorityColor};">
                            ${statusIcon} ${train.name || train.id}
                        </div>
                        <div class="train-status" style="background: ${statusColor}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                            ${train.status.toUpperCase()}
                        </div>
                    </div>
                    <div class="train-details" style="font-size: 12px; color: rgba(255,255,255,0.8); line-height: 1.4;">
                        <div style="margin-bottom: 4px;">
                            <strong>Priority:</strong> <span style="color: ${priorityColor};">${priorityName}</span> |
                            <strong>Delay:</strong> <span style="color: ${train.delay > 5 ? '#ef4444' : '#10b981'};">${train.delay.toFixed(1)} min</span>
                        </div>
                        <div style="margin-bottom: 4px;">
                            <strong>Current:</strong> ${train.current_station || 'Unknown'} →
                            <strong>Next:</strong> ${train.next_station || 'Destination'}
                        </div>
                        <div style="margin-bottom: 4px;">
                            <strong>Progress:</strong> ${Math.round(train.progress || 0)}% |
                            <strong>Status:</strong> ${train.is_moving ? '🚄 Moving' : '🏁 At Station'}
                        </div>
                        ${train.route ? `<div style="font-size: 10px; color: rgba(255,255,255,0.6);">Route: ${train.route.join(' → ')}</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        trainList.innerHTML = trainItems || `
            <div style="text-align: center; color: rgba(255, 255, 255, 0.5); padding: 2rem;">
                <i class="fas fa-train" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                No active trains
            </div>
        `;

        // Update system status indicators
        this.updateSystemStatus();
    }

    updateMetricsDisplay() {
        document.getElementById('throughputMetric').textContent = this.metrics.throughput;
        document.getElementById('delayMetric').textContent = this.metrics.delay.toFixed(1);
        document.getElementById('capacityMetric').textContent = Math.round(this.metrics.capacity);
        document.getElementById('optimizationMetric').textContent = this.metrics.optimization.toFixed(1);
    }

    updateSystemStatus(status, isOnline) {
        const statusElement = document.getElementById('systemStatus');
        const statusDot = document.getElementById('statusDot');

        if (statusElement) {
            statusElement.textContent = status;
        }

        if (statusDot) {
            statusDot.style.background = isOnline ? '#10b981' : '#ef4444';
        }
    }

    runOptimization() {
        console.log('🧠 Running AI optimization...');
        this.showNotification('🤖 AI optimization in progress...', 'info');

        // Show optimization in progress
        document.getElementById('optimizeBtn').disabled = true;
        document.getElementById('optimizeBtn').textContent = '🧠 Optimizing...';

        // Call backend optimization
        fetch('/api/optimization/run', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show optimization results
                    const result = data.result;
                    const milpSolution = result.milp_solution;

                    if (milpSolution.status === 'Optimal') {
                        const metrics = milpSolution.metrics || {};
                        const improvement = metrics.improvement || 0;

                        this.showNotification(`✅ AI optimization completed! Delay improvement: ${improvement.toFixed(1)} minutes`, 'success');

                        // Update metrics
                        this.metrics.optimization = result.optimization_time || 2.5;
                        this.updateMetricsDisplay();

                        // Apply optimization effects to trains
                        if (result.applied_changes) {
                            Object.entries(result.applied_changes).forEach(([trainId, changes]) => {
                                if (this.trains[trainId] && changes.optimized_delay !== undefined) {
                                    const oldDelay = this.trains[trainId].delay;
                                    this.trains[trainId].delay = changes.optimized_delay;

                                    if (oldDelay > changes.optimized_delay) {
                                        this.showNotification(`🚂 Train ${trainId}: delay reduced from ${oldDelay.toFixed(1)} to ${changes.optimized_delay.toFixed(1)} min`, 'info');
                                    }
                                }
                            });

                            this.updateTrainDisplay();
                        }
                    } else {
                        this.showNotification('⚠️ AI optimization completed with suboptimal results', 'warning');
                    }
                } else {
                    this.showNotification('❌ AI optimization failed', 'error');
                }
            })
            .catch(err => {
                console.log('Optimization failed:', err);
                this.showNotification('❌ AI optimization failed - using fallback', 'error');

                // Fallback: simulate some improvement
                Object.values(this.trains).forEach(train => {
                    if (train.delay > 0) {
                        train.delay = Math.max(0, Math.round(train.delay * (0.7 + Math.random() * 0.2)));
                    }
                });

                this.updateTrainDisplay();
            })
            .finally(() => {
                // Re-enable optimization button
                document.getElementById('optimizeBtn').disabled = false;
                document.getElementById('optimizeBtn').textContent = '🧠 Optimize';
            });
    }

    testIncident() {
        console.log('🚨 Testing incident injection...');

        // Create a test incident at Delhi station
        const testIncident = {
            incident: {
                id: `TEST_INC_${Date.now()}`,
                type: 'Track Fault',
                location: {
                    latitude: 28.6139,
                    longitude: 77.2090
                },
                severity: 'high',
                trainNumber: null,
                description: 'Test incident for demonstration',
                reportedBy: 'DEMO_USER'
            }
        };

        this.showNotification('🚨 Injecting test incident at Delhi...', 'warning');

        // Send to backend
        fetch('/api/mobile/incidents/report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(testIncident)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(`✅ Test incident processed! ${data.affectedTrains.length} trains affected, AI optimization ${data.optimizationTriggered ? 'triggered' : 'not needed'}.`, 'success');
            } else {
                this.showNotification('❌ Test incident failed', 'error');
            }
        })
        .catch(err => {
            console.error('Test incident failed:', err);
            this.showNotification('❌ Test incident failed', 'error');
        });
    }

    startMetricsUpdate() {
        // Update metrics every 5 seconds
        setInterval(() => {
            if (this.simulationRunning) {
                // Simulate some metric fluctuations
                this.metrics.capacity += (Math.random() - 0.5) * 5;
                this.metrics.capacity = Math.max(50, Math.min(95, this.metrics.capacity));
                this.updateMetricsDisplay();
            }
        }, 5000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 4px;">${message}</div>
            <div style="font-size: 0.85em; opacity: 0.8;">${new Date().toLocaleTimeString()}</div>
        `;

        const container = document.getElementById('notifications') || document.body;
        container.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    handleSimulationUpdate(data) {
        console.log('Simulation update:', data);

        // Update train positions and status from real backend data
        if (data.trains) {
            Object.values(data.trains).forEach(trainData => {
                // Update or create train data
                if (!this.trains[trainData.id]) {
                    this.trains[trainData.id] = {
                        id: trainData.id,
                        name: trainData.id,
                        position: { lat: 0, lng: 0 },
                        status: 'scheduled',
                        delay: 0,
                        priority: trainData.priority,
                        progress: 0
                    };
                }

                // Update train data with real-time information
                const train = this.trains[trainData.id];
                train.delay = trainData.delay;
                train.status = trainData.status;
                train.priority = trainData.priority;
                train.current_station = trainData.current_station;
                train.next_station = trainData.next_station;
                train.progress = (trainData.current_position / Math.max(1, trainData.route.length - 1)) * 100;
                train.is_moving = trainData.is_moving;
                train.route = trainData.route;

                // Update visual position if available
                if (trainData.visual_position) {
                    const [lat, lng, progress] = trainData.visual_position;
                    train.position = { lat, lng };

                    // Update or create marker with new position
                    if (this.trainMarkers[trainData.id]) {
                        this.trainMarkers[trainData.id].setLatLng([lat, lng]);

                        // Update popup content with latest data
                        const popupContent = this.generateTrainPopupContent(train);
                        this.trainMarkers[trainData.id].setPopupContent(popupContent);
                    } else {
                        // Create new marker if it doesn't exist
                        this.createTrainMarker(train);
                    }
                }
            });

            this.updateTrainDisplay();
        }

        // Update metrics if available
        if (data.metrics) {
            this.metrics = data.metrics;
            this.updateMetricsDisplay();
        }
    }

    generateTrainPopupContent(train) {
        // Determine train color based on priority
        let trainColor = '#6366f1';
        if (train.priority === 1) trainColor = '#10b981'; // Express
        else if (train.priority === 2) trainColor = '#3b82f6'; // Passenger
        else if (train.priority === 3) trainColor = '#f59e0b'; // Freight
        else if (train.priority === 4) trainColor = '#8b5cf6'; // Maintenance

        let statusIcon = '🚂';
        if (train.status === 'delayed') statusIcon = '⚠️';
        else if (train.status === 'completed') statusIcon = '✅';
        else if (train.status === 'waiting') statusIcon = '⏸️';

        return `
            <div style="color: #333; font-family: Arial; min-width: 220px;">
                <h4 style="margin: 0 0 8px 0; color: ${trainColor};">${statusIcon} ${train.name || train.id}</h4>
                <div style="font-size: 12px; line-height: 1.4;">
                    <p style="margin: 2px 0;"><strong>ID:</strong> ${train.id}</p>
                    <p style="margin: 2px 0;"><strong>Priority:</strong> ${this.getPriorityName(train.priority)}</p>
                    <p style="margin: 2px 0;"><strong>Status:</strong> <span style="color: ${train.status === 'running' ? '#10b981' : train.status === 'delayed' ? '#ef4444' : '#6366f1'};">${train.status.toUpperCase()}</span></p>
                    <p style="margin: 2px 0;"><strong>Current:</strong> ${train.current_station || 'Unknown'}</p>
                    <p style="margin: 2px 0;"><strong>Next:</strong> ${train.next_station || 'Destination'}</p>
                    ${train.current_track ? `<p style="margin: 2px 0;"><strong>Track:</strong> <span style="color: #f59e0b;">${train.current_track}</span></p>` : ''}
                    <p style="margin: 2px 0;"><strong>Delay:</strong> ${train.delay.toFixed(1)} min</p>
                    <p style="margin: 2px 0;"><strong>Progress:</strong> ${Math.round(train.progress || 0)}%</p>
                    ${train.is_moving ? '<p style="margin: 2px 0; color: #10b981;"><strong>🚄 Moving</strong></p>' : '<p style="margin: 2px 0; color: #6366f1;"><strong>🏁 At Station</strong></p>'}
                </div>
            </div>
        `;
    }

    getPriorityName(priority) {
        const priorityNames = {
            1: 'EXPRESS',
            2: 'PASSENGER',
            3: 'FREIGHT',
            4: 'MAINTENANCE'
        };
        return priorityNames[priority] || 'UNKNOWN';
    }

    handleTrainEvent(event) {
        console.log('Train event:', event);

        if (event.type === 'train_moved') {
            this.showNotification(`🚂 Train ${event.data.train_id} moved from ${event.data.from_station} to ${event.data.to_station}`, 'info');
        } else if (event.type === 'train_delayed') {
            this.showNotification(`⏰ Train ${event.data.train_id} delayed: ${event.data.reason}`, 'warning');
        } else if (event.type === 'train_completed') {
            this.showNotification(`✅ Train ${event.data.train_id} completed journey`, 'success');
        }
    }

    handleIncidentReport(data) {
        console.log('Incident reported:', data);

        const incident = data.incident;
        const location = incident.location;

        // Add incident marker to map
        const incidentMarker = L.marker([location.latitude, location.longitude], {
            icon: L.divIcon({
                className: 'incident-marker',
                html: `<div style="
                    background: #ef4444;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 50%;
                    font-size: 12px;
                    font-weight: bold;
                    border: 2px solid white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                ">🚨</div>`,
                iconSize: [30, 30],
                iconAnchor: [15, 15]
            })
        }).addTo(this.map).bindPopup(`
            <div style="color: #333; font-family: Arial;">
                <h4 style="margin: 0 0 5px 0; color: #ef4444;">⚠️ ${incident.type}</h4>
                <p style="margin: 0; font-size: 12px;">Severity: ${incident.severity}</p>
                <p style="margin: 0; font-size: 12px;">Affected trains: ${data.affected_trains}</p>
                <p style="margin: 0; font-size: 12px;">Time: ${new Date(incident.timestamp).toLocaleTimeString()}</p>
            </div>
        `);

        // Show notification
        this.showNotification(`🚨 ${incident.type} reported! ${data.affected_trains} trains affected. AI optimization ${data.optimization_result ? 'triggered' : 'not needed'}.`, 'error');

        // Auto-remove incident marker after 5 minutes
        setTimeout(() => {
            this.map.removeLayer(incidentMarker);
        }, 300000);
    }

    handleTrainReroute(event) {
        console.log('Train rerouted:', event);

        const trainId = event.data.train_id;
        const oldRoute = event.data.old_route;
        const newRoute = event.data.new_route;

        this.showNotification(`🔄 Train ${trainId} rerouted due to ${event.data.reason}. New route: ${newRoute.join(' → ')}`, 'info');

        // Update train route in our data
        if (this.trains[trainId]) {
            this.trains[trainId].route = newRoute;
        }
    }

    handleIncidentInjection(event) {
        console.log('Incident injected:', event);

        const incident = event.data.incident;
        const affectedTracks = event.data.affected_tracks;

        // Visual indication of blocked tracks
        this.showNotification(`🚫 ${affectedTracks.length} tracks blocked due to ${incident.type}`, 'warning');

        // Block tracks visually
        affectedTracks.forEach(trackId => {
            this.blockTrackVisually(trackId);
        });
    }

    blockTrackVisually(trackId) {
        // Find track line and change its appearance
        if (this.trackLines && this.trackLines[trackId]) {
            this.trackLines[trackId].setStyle({
                color: '#ef4444',
                weight: 4,
                opacity: 0.9,
                dashArray: '10, 10'  // Dashed line for blocked track
            });

            // Update popup status
            const statusElement = document.getElementById(`track-status-${trackId}`);
            if (statusElement) {
                statusElement.textContent = 'BLOCKED';
                statusElement.style.color = '#ef4444';
                statusElement.style.fontWeight = 'bold';
            }

            // Update button visibility
            const disruptBtn = document.getElementById(`disrupt-btn-${trackId}`);
            const restoreBtn = document.getElementById(`restore-btn-${trackId}`);
            if (disruptBtn) disruptBtn.style.display = 'none';
            if (restoreBtn) restoreBtn.style.display = 'inline-block';
        }

        // Also check reverse direction
        const reverseTrackId = trackId.split('-').reverse().join('-');
        if (this.trackLines && this.trackLines[reverseTrackId]) {
            this.trackLines[reverseTrackId].setStyle({
                color: '#ef4444',
                weight: 4,
                opacity: 0.9,
                dashArray: '10, 10'
            });

            const reverseStatusElement = document.getElementById(`track-status-${reverseTrackId}`);
            if (reverseStatusElement) {
                reverseStatusElement.textContent = 'BLOCKED';
                reverseStatusElement.style.color = '#ef4444';
                reverseStatusElement.style.fontWeight = 'bold';
            }

            // Update reverse direction buttons too
            const reverseDisruptBtn = document.getElementById(`disrupt-btn-${reverseTrackId}`);
            const reverseRestoreBtn = document.getElementById(`restore-btn-${reverseTrackId}`);
            if (reverseDisruptBtn) reverseDisruptBtn.style.display = 'none';
            if (reverseRestoreBtn) reverseRestoreBtn.style.display = 'inline-block';
        }
    }

    unblockTrackVisually(trackId) {
        // Restore normal track appearance
        if (this.trackLines && this.trackLines[trackId]) {
            this.trackLines[trackId].setStyle({
                color: '#6366f1',
                weight: 3,
                opacity: 0.7,
                dashArray: null
            });

            const statusElement = document.getElementById(`track-status-${trackId}`);
            if (statusElement) {
                statusElement.textContent = 'Active';
                statusElement.style.color = '#10b981';
                statusElement.style.fontWeight = 'normal';
            }

            // Update button visibility
            const disruptBtn = document.getElementById(`disrupt-btn-${trackId}`);
            const restoreBtn = document.getElementById(`restore-btn-${trackId}`);
            if (disruptBtn) disruptBtn.style.display = 'inline-block';
            if (restoreBtn) restoreBtn.style.display = 'none';
        }
    }

    disruptTrack(trackId) {
        console.log(`🚨 Disrupting track: ${trackId}`);

        // Show loading state
        this.showNotification(`🚨 Disrupting track ${trackId}...`, 'warning');

        // Call backend API to block track
        fetch('/api/tracks/block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                track_id: trackId,
                reason: 'Manual disruption from dashboard'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(`✅ Track ${trackId} disrupted! ${data.affected_trains.length} trains affected. ${data.optimization_triggered ? 'AI optimization triggered.' : ''}`, 'error');

                // Update track status locally
                this.trackStatus[trackId] = 'blocked';
                this.blockTrackVisually(trackId);

                // Log the event
                this.logEvent('track_disruption', `Track ${trackId} manually blocked - ${data.affected_trains.length} trains affected`, 'error');
            } else {
                this.showNotification(`❌ Failed to disrupt track: ${data.error}`, 'error');
            }
        })
        .catch(err => {
            console.error('Track disruption failed:', err);
            this.showNotification('❌ Track disruption failed', 'error');
        });
    }

    restoreTrack(trackId) {
        console.log(`✅ Restoring track: ${trackId}`);

        // Show loading state
        this.showNotification(`🔧 Restoring track ${trackId}...`, 'info');

        // Call backend API to unblock track
        fetch('/api/tracks/unblock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                track_id: trackId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification(`✅ Track ${trackId} restored!`, 'success');

                // Update track status locally
                this.trackStatus[trackId] = 'available';
                this.unblockTrackVisually(trackId);

                // Log the event
                this.logEvent('track_restoration', `Track ${trackId} restored and available`, 'success');
            } else {
                this.showNotification(`❌ Failed to restore track: ${data.error}`, 'error');
            }
        })
        .catch(err => {
            console.error('Track restoration failed:', err);
            this.showNotification('❌ Track restoration failed', 'error');
        });
    }

    handleTrackBlocked(data) {
        console.log('Track blocked event:', data);

        const trackId = data.track_id;
        this.trackStatus[trackId] = 'blocked';
        this.blockTrackVisually(trackId);

        this.showNotification(`🚫 Track ${trackId} blocked: ${data.reason}. ${data.affected_trains.length} trains affected.`, 'error');
    }

    handleTrackUnblocked(data) {
        console.log('Track unblocked event:', data);

        const trackId = data.track_id;
        this.trackStatus[trackId] = 'available';
        this.unblockTrackVisually(trackId);

        this.showNotification(`✅ Track ${trackId} restored and available.`, 'success');
    }

    handleMobileIncidentReport(data) {
        console.log('📱 Mobile incident report received:', data);

        const incident = data.incident;
        const affectedTrains = data.affected_trains || 0;

        // Show prominent notification for mobile incident
        this.showNotification(
            `📱 MOBILE ALERT: ${incident.type} reported at ${incident.location.name || 'Unknown Location'}. ${affectedTrains} trains affected.`,
            'error'
        );

        // Log the mobile incident
        this.logEvent('mobile_incident', `${incident.type} reported via mobile app - ${affectedTrains} trains affected`, 'error');

        // Update AI status indicator
        const aiStatus = document.getElementById('aiStatusIndicator');
        if (aiStatus) {
            if (data.optimization_result) {
                aiStatus.textContent = '🧠 Optimizing...';
                aiStatus.style.color = '#f59e0b';

                // Reset after a few seconds
                setTimeout(() => {
                    aiStatus.textContent = '🧠 Ready';
                    aiStatus.style.color = '#10b981';
                }, 5000);
            }
        }

        // If incident affects tracks, mark them as blocked visually
        if (incident.type === 'Signal Failure' || incident.type === 'Track Fault') {
            // Find tracks near the incident location
            const location = incident.location;
            if (location.latitude && location.longitude) {
                this.markTracksNearIncident(location.latitude, location.longitude, incident.type);
            }
        }

        // Show detailed incident information
        this.showIncidentDetails(incident, affectedTrains);
    }

    markTracksNearIncident(lat, lng, incidentType) {
        // Find tracks within 2km of incident location
        const incidentRadius = 2.0; // km

        Object.entries(this.trackLines).forEach(([trackId, trackLine]) => {
            const trackBounds = trackLine.getBounds();
            const trackCenter = trackBounds.getCenter();

            // Calculate distance from incident to track center
            const distance = this.calculateDistance(lat, lng, trackCenter.lat, trackCenter.lng);

            if (distance <= incidentRadius) {
                console.log(`🚫 Marking track ${trackId} as affected by mobile incident`);

                // Mark track as blocked
                this.trackStatus[trackId] = 'blocked';
                this.blockTrackVisually(trackId);

                // Show notification for each affected track
                this.showNotification(`🚫 Track ${trackId} blocked due to ${incidentType}`, 'warning');
            }
        });
    }

    calculateDistance(lat1, lng1, lat2, lng2) {
        // Haversine formula for distance calculation
        const R = 6371; // Earth's radius in km
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLng = (lng2 - lng1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    showIncidentDetails(incident, affectedTrains) {
        // Create incident details popup
        const incidentPopup = document.createElement('div');
        incidentPopup.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.95);
            color: white;
            padding: 25px;
            border-radius: 15px;
            border: 2px solid #ef4444;
            z-index: 10000;
            max-width: 400px;
            backdrop-filter: blur(10px);
        `;

        incidentPopup.innerHTML = `
            <div style="text-align: center;">
                <h3 style="color: #ef4444; margin: 0 0 15px 0;">📱 Mobile Incident Report</h3>
                <div style="text-align: left; font-size: 14px; line-height: 1.6;">
                    <p><strong>Type:</strong> ${incident.type}</p>
                    <p><strong>Severity:</strong> ${incident.severity || 'Medium'}</p>
                    <p><strong>Location:</strong> ${incident.location.name || 'Unknown'}</p>
                    <p><strong>Reported by:</strong> ${incident.reported_by || 'Field Staff'}</p>
                    <p><strong>Affected Trains:</strong> ${affectedTrains}</p>
                    <p><strong>Time:</strong> ${new Date().toLocaleTimeString()}</p>
                    ${incident.description ? `<p><strong>Description:</strong> ${incident.description}</p>` : ''}
                </div>
                <button onclick="this.parentElement.parentElement.remove()"
                        style="background: #ef4444; color: white; border: none; padding: 10px 20px; border-radius: 5px; margin-top: 15px; cursor: pointer;">
                    Close
                </button>
            </div>
        `;

        document.body.appendChild(incidentPopup);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (incidentPopup.parentElement) {
                incidentPopup.remove();
            }
        }, 10000);
    }

    handleStationConflict(data) {
        console.log('🚉 Station conflict resolved:', data);

        const stationId = data.station_id;
        const priorityTrain = data.priority_train;
        const allTrains = data.trains;

        // Show notification about conflict resolution
        this.showNotification(
            `🚉 Station conflict at ${stationId}: Priority given to train ${priorityTrain} (${allTrains.length} trains involved)`,
            'warning'
        );

        // Log the conflict resolution
        this.logEvent('conflict_resolution', `Station conflict at ${stationId} resolved - priority to train ${priorityTrain}`, 'warning');

        // Highlight the station temporarily
        if (this.stationMarkers[stationId]) {
            const marker = this.stationMarkers[stationId];
            const originalIcon = marker.getIcon();

            // Change to conflict resolution icon
            marker.setIcon({
                ...originalIcon,
                className: 'station-marker conflict-resolved',
                html: `<div style="background: #f59e0b; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: bold;">⚡</div>`
            });

            // Reset after 3 seconds
            setTimeout(() => {
                marker.setIcon(originalIcon);
            }, 3000);
        }
    }

    handlePriorityTrackAssignment(data) {
        console.log('🎯 Priority track assignment:', data);

        const trackId = data.track_id;
        const trainId = data.train_id;
        const waitingCount = data.waiting_trains_count;

        // Show notification about priority assignment
        this.showNotification(
            `🎯 Priority track access: Train ${trainId} assigned to track ${trackId} (${waitingCount} trains waiting)`,
            'info'
        );

        // Log the priority assignment
        this.logEvent('priority_assignment', `Train ${trainId} given priority access to track ${trackId}`, 'info');

        // Briefly highlight the track
        if (this.trackLines[trackId]) {
            const trackLine = this.trackLines[trackId];
            const originalColor = trackLine.options.color;

            // Flash with priority color
            trackLine.setStyle({ color: '#f59e0b', weight: 6 });

            setTimeout(() => {
                trackLine.setStyle({ color: originalColor, weight: 4 });
            }, 2000);
        }
    }

    initializeStatusIndicators() {
        // Create floating status panel for real-time system status
        const statusPanel = document.createElement('div');
        statusPanel.id = 'systemStatusPanel';
        statusPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            min-width: 250px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        `;

        statusPanel.innerHTML = `
            <h4 style="margin: 0 0 10px 0; color: #10b981; font-size: 14px;">
                <i class="fas fa-tachometer-alt"></i> System Status
            </h4>
            <div id="systemStatusContent">
                <div class="status-item">
                    <span class="status-label">Simulation:</span>
                    <span id="simulationStatusIndicator" class="status-value">⏹️ Stopped</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Active Trains:</span>
                    <span id="activeTrainsIndicator" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Blocked Tracks:</span>
                    <span id="blockedTracksIndicator" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">AI Status:</span>
                    <span id="aiStatusIndicator" class="status-value">🧠 Ready</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Track Occupancy:</span>
                    <span id="trackOccupancyIndicator" class="status-value">0/19</span>
                </div>
            </div>
            <div style="margin-top: 15px; border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 10px;">
                <h5 style="margin: 0 0 8px 0; color: #10b981; font-size: 12px;">
                    <i class="fas fa-list"></i> Event Log
                </h5>
                <div id="eventLogContainer" style="max-height: 200px; overflow-y: auto;">
                    <div style="text-align: center; color: rgba(255, 255, 255, 0.5); padding: 10px; font-size: 11px;">
                        No events yet
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(statusPanel);

        // Add CSS for status items
        const style = document.createElement('style');
        style.textContent = `
            .status-item {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
                font-size: 12px;
            }
            .status-label {
                color: rgba(255, 255, 255, 0.7);
            }
            .status-value {
                color: #10b981;
                font-weight: bold;
            }
        `;
        document.head.appendChild(style);
    }

    updateSystemStatus() {
        // Update simulation status
        const simulationStatus = document.getElementById('simulationStatusIndicator');
        if (simulationStatus) {
            if (this.simulationRunning) {
                simulationStatus.textContent = '🚄 Running';
                simulationStatus.style.color = '#10b981';
            } else {
                simulationStatus.textContent = '⏹️ Stopped';
                simulationStatus.style.color = '#6366f1';
            }
        }

        // Update active trains count
        const activeTrains = document.getElementById('activeTrainsIndicator');
        if (activeTrains) {
            const runningTrains = Object.values(this.trains).filter(t => t.status === 'running').length;
            activeTrains.textContent = `${runningTrains}/${Object.keys(this.trains).length}`;
            activeTrains.style.color = runningTrains > 0 ? '#10b981' : '#6366f1';
        }

        // Update blocked tracks count
        const blockedTracks = document.getElementById('blockedTracksIndicator');
        if (blockedTracks) {
            const blockedCount = Object.values(this.trackStatus).filter(status => status === 'blocked').length;
            blockedTracks.textContent = blockedCount.toString();
            blockedTracks.style.color = blockedCount > 0 ? '#ef4444' : '#10b981';
        }

        // Update track occupancy count
        const trackOccupancy = document.getElementById('trackOccupancyIndicator');
        if (trackOccupancy) {
            const occupiedTracks = new Set();
            Object.values(this.trains).forEach(train => {
                if (train.current_track && train.status === 'running') {
                    occupiedTracks.add(train.current_track);
                }
            });
            const totalTracks = 19; // Total number of tracks in the network
            trackOccupancy.textContent = `${occupiedTracks.size}/${totalTracks}`;
            trackOccupancy.style.color = occupiedTracks.size > 0 ? '#10b981' : '#6366f1';
        }
    }

    logEvent(type, message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const event = {
            id: Date.now() + Math.random(),
            timestamp,
            type,
            message,
            level
        };

        // Add to event log
        this.eventLog.unshift(event);

        // Keep only the latest entries
        if (this.eventLog.length > this.maxLogEntries) {
            this.eventLog = this.eventLog.slice(0, this.maxLogEntries);
        }

        // Update event log display
        this.updateEventLogDisplay();

        // Show notification for important events
        if (level === 'error' || level === 'warning') {
            this.showNotification(message, level);
        }
    }

    updateEventLogDisplay() {
        const eventLogContainer = document.getElementById('eventLogContainer');
        if (!eventLogContainer) return;

        const recentEvents = this.eventLog.slice(0, 10); // Show last 10 events

        eventLogContainer.innerHTML = recentEvents.map(event => {
            const levelIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            }[event.level] || 'ℹ️';

            const levelColor = {
                'info': '#3b82f6',
                'success': '#10b981',
                'warning': '#f59e0b',
                'error': '#ef4444'
            }[event.level] || '#3b82f6';

            return `
                <div class="event-log-item" style="
                    padding: 8px 12px;
                    margin: 4px 0;
                    background: rgba(255, 255, 255, 0.05);
                    border-left: 3px solid ${levelColor};
                    border-radius: 4px;
                    font-size: 12px;
                    line-height: 1.4;
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: ${levelColor}; font-weight: bold;">
                            ${levelIcon} ${event.type.toUpperCase()}
                        </span>
                        <span style="color: rgba(255, 255, 255, 0.6); font-size: 10px;">
                            ${event.timestamp}
                        </span>
                    </div>
                    <div style="color: rgba(255, 255, 255, 0.9); margin-top: 2px;">
                        ${event.message}
                    </div>
                </div>
            `;
        }).join('');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Starting AI Train Traffic Control System...');
    window.app = new TrafficControlApp();
});
