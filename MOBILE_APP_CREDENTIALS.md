# 📱 Mobile App Integration Credentials & Setup

**AI-Powered Train Traffic Control System - Mobile App Integration**  
**Team SHHHI - Smart India Hackathon 2025**

---

## 🚀 Quick Start for Mobile App Developer

### **Server Configuration**
```
Base URL: http://localhost:5000
WebSocket URL: ws://localhost:5000/simulation
API Version: v1
```

### **Authentication Endpoint**
```
POST /api/mobile/auth
Content-Type: application/json

Request Body:
{
  "employee_id": "EMP001",
  "device_id": "DEVICE_ANDROID_001"
}

Response:
{
  "success": true,
  "auth_token": "mobile_token_EMP001_DEVICE_ANDROID_001",
  "employee_id": "EMP001",
  "permissions": ["report_incident", "view_status"],
  "server_url": "http://localhost:5000",
  "message": "Authentication successful"
}
```

---

## 📱 Core Mobile App API Endpoints

### **1. Report Incident (Main Feature)**
```
POST /api/mobile/incidents/report
Content-Type: application/json
Authorization: Bearer {auth_token}

Request Body:
{
  "incident": {
    "id": "INC_001_20250911_143022",
    "type": "Track Fault",
    "location": {
      "latitude": 28.6139,
      "longitude": 77.2090
    },
    "severity": "high",
    "trainNumber": "12951",
    "description": "Crack observed in rail track",
    "reportedBy": "EMP001"
  }
}

Response:
{
  "success": true,
  "optimizationTriggered": true,
  "affectedTrains": ["12951", "19037"],
  "emergencyDelayApplied": 30,
  "estimatedImpact": "high",
  "message": "Incident processed. 2 trains affected, AI optimization triggered."
}
```

### **2. Get System Status**
```
GET /api/mobile/status

Response:
{
  "system_online": true,
  "simulation_running": true,
  "simulation_paused": false,
  "current_time": 45.2,
  "active_trains": 8,
  "total_trains": 9,
  "server_time": "2025-09-11T14:30:22.123Z",
  "last_optimization": 15
}
```

### **3. Get Nearby Trains**
```
POST /api/mobile/trains/nearby
Content-Type: application/json

Request Body:
{
  "latitude": 28.6139,
  "longitude": 77.2090,
  "radius_km": 10.0
}

Response:
{
  "success": true,
  "trains": [
    {
      "id": "12951",
      "status": "running",
      "delay": 5.2,
      "priority": 1,
      "current_station": "DEL",
      "next_station": "GZB"
    }
  ],
  "count": 1,
  "search_radius_km": 10.0
}
```

---

## 🔧 Implementation Guide

### **Step 1: Setup HTTP Client**
```kotlin
// Retrofit setup for API calls
val retrofit = Retrofit.Builder()
    .baseUrl("http://localhost:5000/")
    .addConverterFactory(GsonConverterFactory.create())
    .build()

val apiService = retrofit.create(RailwayApiService::class.java)
```

### **Step 2: Authentication**
```kotlin
// Authenticate user
val authRequest = AuthRequest("EMP001", "DEVICE_ANDROID_001")
val response = apiService.authenticate(authRequest)

if (response.isSuccessful) {
    val authToken = response.body()?.auth_token
    // Store token for future requests
}
```

### **Step 3: Report Incident (Main Feature)**
```kotlin
// Create incident report
val incident = IncidentReport(
    id = "INC_${System.currentTimeMillis()}",
    type = "Track Fault",
    location = Location(latitude, longitude),
    severity = "high",
    trainNumber = trainNumber,
    description = description,
    reportedBy = employeeId
)

val request = IncidentRequest(incident)
val response = apiService.reportIncident(request)

if (response.isSuccessful) {
    // Show success message
    // Display optimization results
}
```

---

## 📊 Incident Types & Severity Levels

### **Incident Types**
- `"Signal Failure"` - Traffic signal malfunction
- `"Track Fault"` - Physical track damage
- `"Engine Breakdown"` - Train mechanical failure
- `"Security Alert"` - Security-related incident
- `"Infrastructure Damage"` - Station/bridge damage
- `"Weather Related"` - Weather-caused disruption
- `"Other"` - Miscellaneous incidents

### **Severity Levels**
- `"low"` - Minor impact, 5-minute delays
- `"medium"` - Moderate impact, 15-minute delays
- `"high"` - Major impact, 30-minute delays

---

## 🎯 Demo Flow for Hackathon

### **Perfect Demo Scenario**
1. **Start the backend server**: `python backend/app.py`
2. **Open web dashboard**: http://localhost:5000
3. **Start simulation** on web dashboard
4. **Use mobile app** to report incident:
   - Type: "Track Fault"
   - Location: Current GPS (or Delhi coordinates: 28.6139, 77.2090)
   - Severity: "high"
5. **Watch the magic happen**:
   - Red alert appears on web dashboard
   - Affected trains get delayed
   - AI optimization triggers automatically
   - Routes get recalculated
   - Trains change paths in real-time

### **Judge-Friendly Talking Points**
- "Real-world incident reported from field in 5 seconds"
- "AI system immediately processes and optimizes"
- "Complete end-to-end operational ecosystem"
- "Seamless integration between field and control room"

---

## 🔗 WebSocket Integration (Optional)

For real-time updates, connect to WebSocket:
```javascript
const socket = io('http://localhost:5000/simulation');

socket.on('incident_reported', (data) => {
    // Handle real-time incident updates
    console.log('Incident processed:', data);
});

socket.on('optimization_result', (data) => {
    // Handle AI optimization results
    console.log('AI optimization completed:', data);
});
```

---

## 🚨 Emergency Testing

If you need to test quickly without mobile app:
```bash
# Test incident reporting with curl
curl -X POST http://localhost:5000/api/mobile/incidents/report \
  -H "Content-Type: application/json" \
  -d '{
    "incident": {
      "id": "TEST_001",
      "type": "Track Fault",
      "location": {"latitude": 28.6139, "longitude": 77.2090},
      "severity": "high"
    }
  }'
```

---

## 📞 Support

- **Backend running on**: http://localhost:5000
- **API documentation**: All endpoints listed above
- **Real-time events**: WebSocket at ws://localhost:5000/simulation
- **Test credentials**: Any employee_id + device_id combination works

**System is ready for mobile app integration! 🚂📱**
